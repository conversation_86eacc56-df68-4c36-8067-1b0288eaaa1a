import { JSX, useState } from 'react';
import { Scrollbar } from '@betgames/bg-tools';
import { ToggleSwitch } from '../ToggleSwitch';
import classes from './WinBoard.module.scss';
import { useData } from './hooks';
import { DateFilter, TAB } from '../enums/enums';
import { Wins } from './Wins';
import { Multipliers } from './Multipliers';
import { WinsSkeleton } from './Wins/WinsSkeleton/WinsSkeleton';
import { MultiplierSkeleton } from './Multipliers/MultiplierSkeleton/MultiplierSkeleton';

export const WinBoard = (): JSX.Element => {
    const [activeTab, setActiveTab] = useState<TAB>(TAB.WINS);
    const [activeDateFilter, setActiveDateFilter] = useState<DateFilter>(DateFilter.DAY);
    const { winsData, multipliersData } = useData(activeTab, activeDateFilter);

    const renderContent = () => {
        if (activeTab === TAB.WINS) {
            return winsData.length ? <Wins data={winsData} /> : <WinsSkeleton />;
        }

        return multipliersData.length ? (
            <Multipliers data={multipliersData} />
        ) : (
            <MultiplierSkeleton />
        );
    };
    console.log('test');

    return (
        <Scrollbar className={classes.scrollbar}>
            <div className={classes.filterContainer}>
                <ToggleSwitch
                    enumObject={TAB}
                    activeTab={activeTab}
                    onTabChange={setActiveTab}
                    withBorder
                    className={classes.tabFilterMargins}
                />
                <ToggleSwitch
                    enumObject={DateFilter}
                    activeTab={activeDateFilter}
                    onTabChange={setActiveDateFilter}
                    className={classes.dateFilterMargins}
                />
            </div>
            {renderContent()}
        </Scrollbar>
    );
};
