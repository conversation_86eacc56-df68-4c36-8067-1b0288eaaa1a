import { ElementRef, JSX, useRef } from 'react';
import classNames from 'classnames';
import { FlashRouletteRunType, useElementOnResize } from '@bg-shared';
import { useViews } from '../../../../hooks/useViews';
import classes from './ZeroSectorSvg.module.scss';

interface IZeroSectorSvgProps {
    multiplier: string;
    multiplierType: FlashRouletteRunType;
}

export const ZeroSectorSvg = (props: IZeroSectorSvgProps): JSX.Element => {
    const zeroSectorRef = useRef<ElementRef<'div'>>();
    const zeroSectorSvgRef = useRef<ElementRef<'svg'>>();
    const polygonRef = useRef<ElementRef<'polygon'>>(null);
    const textRef = useRef<ElementRef<'text'>>(null);
    const zeroStrokeOffset = Number(classes.zeroStrokeWidth) / 2;
    const { isMobileViewRef } = useViews();

    useElementOnResize(
        zeroSectorRef,
        (size) => {
            if (!polygonRef.current || !textRef.current) {
                return;
            }

            let points: Array<[number, number]>;

            if (isMobileViewRef.current) {
                points = [
                    [zeroStrokeOffset, size.height / 2],
                    [size.width / 2, zeroStrokeOffset],
                    [size.width - zeroStrokeOffset, size.height / 2],
                    [size.width - zeroStrokeOffset, size.height - zeroStrokeOffset],
                    [zeroStrokeOffset, size.height - zeroStrokeOffset],
                ];
            } else {
                points = [
                    [size.width / 2, zeroStrokeOffset],
                    [zeroStrokeOffset, size.height / 2],
                    [size.width / 2, size.height - zeroStrokeOffset],
                    [size.width, size.height - zeroStrokeOffset],
                    [size.width, zeroStrokeOffset],
                ];

                textRef.current.setAttribute(
                    'transform',
                    `rotate(-90, ${size.width / 2}, ${size.height / 2})`,
                );
            }

            polygonRef.current.setAttribute(
                'points',
                points.map((pair) => pair.join(',')).join(' '),
            );
            textRef.current.setAttribute('x', `${size.width / 2}`);
            textRef.current.setAttribute('y', `${size.height / 2}`);
        },
        [props.multiplier],
        0,
    );

    console.log('test');

    return (
        <div ref={zeroSectorRef} className={classes.container}>
            <svg
                ref={zeroSectorSvgRef}
                xmlns="http://www.w3.org/2000/svg"
                className={classNames(classes.sector, classes[props.multiplierType])}
            >
                {/* Using svg filter instead css "filter: drop-shadow" for Safari support */}
                <defs>
                    <filter id="inner-shadow" x="-50%" y="-50%" width="200%" height="200%">
                        <feGaussianBlur in="SourceAlpha" stdDeviation="3" result="blur" />
                        <feOffset in="blur" dx="0" dy="2" result="offsetBlur" />
                        <feFlood floodColor="var(--zero-sector-stroke)" result="color" />
                        <feComposite in2="offsetBlur" operator="in" />
                        <feMerge>
                            <feMergeNode />
                            <feMergeNode in="SourceGraphic" />
                        </feMerge>
                    </filter>
                </defs>
                <polygon ref={polygonRef} filter="url(#inner-shadow)" />
                <text ref={textRef}>{props.multiplier}</text>
            </svg>
        </div>
    );
};
