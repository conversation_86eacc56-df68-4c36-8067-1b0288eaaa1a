import { JS<PERSON>, PropsWithChildren, useEffect, useMemo, useRef, useState } from 'react';
import classNames from 'classnames';
import { keyBy, uniq } from 'lodash-unified';
import { GameDefinition, RouletteOptionsClasses } from 'betgames-contract-js';
import { useStore } from '@betgames/bg-state-manager';
import { uaParser, useAfterUpdate, useLandscape } from '@betgames/bg-tools';
import { GameContent } from '@bg-components/GameContent';
import { RotateRestriction } from '@bg-components/RotateRestriction';
import { TopNotifications } from '@bg-components/GameContent/TopNotifications';
import { GameMessages } from '@bg-components/GameMessages';
import { GameInfo } from '@bg-components/GameContent/GameInfo';
import { PlayerWonMessage } from '@bg-components/PlayerWonMessage';
import { LoginMessage } from '@bg-components/LoginMessage';
import { Clock } from '@bg-components/Clock';
import { GameTimer } from '@bg-components/GameContent/GameTimer';
import { LiveInfo } from '@bg-components/GameContent/LiveInfo';
import { GamePanel } from '@bg-components/GamePanel';
import { ButtonsList } from '@bg-components/ButtonsList';
import { Curtain } from '@bg-components/GameContent/Curtain';
import { RoundInfoContainer } from '@bg-components/RoundInfo/RoundInfoContainer';
import { RoundTimer } from '@bg-components/GameContent/RoundTimer';
import { CasinoContainer } from '@bg-components/CasinoContainer';
import { Promotions } from '@bg-components/Promotions';
import { Chips } from '@bg-components/CasinoChips/Chips';
import { LastResults } from '@bg-components/Roulette/LastResults';
import { PlayerStats, PlayerStatsFooter } from '@bg-components/PlayerStats';
import { TableButton } from '@bg-components/Buttons/TableButton';
import { StatisticsButton } from '@bg-components/Buttons/StatisticsButton';
import { SoundButton } from '@bg-components/Buttons/SoundButton';
import { VideoButton } from '@bg-components/Buttons/VideoButton';
import { FullscreenButton } from '@bg-components/Buttons/FullscreenButton';
import { ChatButton } from '@bg-components/Buttons/ChatButton';
import { TipsButton } from '@bg-components/Buttons/TipsButton';
import { AdaptiveContainer } from '@bg-components/AdaptiveContainer';
import { PortraitRightPanel } from '@bg-components/PortraitRightPanel';
import { AutoPlayButton } from '@bg-components/Buttons/AutoPlayButton';
import {
    analyticsApi,
    authStore,
    casinoDraftBetAmountsEntity,
    CasinoServiceFactory,
    GameId,
    GameMessagePlacement,
    GameMessageType,
    gameRouletteSelectors,
    GameRouletteStore,
    gamesMessagesEntity,
    gamesStatusEntity,
    gamesStatusSelectors,
    getIframeHeightToFitVideo,
    IChipAmounts,
    ILotteryGameId,
    ILotteryItem,
    IRouletteMultipliersData,
    partnerSettingsStore,
    playerSettingsSelectors,
    playerSettingsStore,
    ROULETTE_LAST_RESULT_DELAY_FOR_GUI,
    RouletteValidationService,
    useCancelDraw,
    useCasinoBetting,
    useLastResultsList,
    useStaticHeightGame,
    useTopNotification,
    t,
    gamesOptionsEntity,
    gamesOptionsSelectors,
    useAnyBetsPlaced,
} from '@bg-shared';
import { Message } from '@bg-components/GameMessages/Message';
import { CollapseButton } from '@bg-components/Buttons/CollapseButton';
import { InitialPageSkeleton } from '@bg-components/Skeleton';
import { usePayoutModal } from '@bg-components/Modals/PayoutModal';
import { useAutoPlay } from '@bg-components/Modals/CasinoAutoPlayModal';
import { LiveInfoMobile } from './components/LiveInfoMobile';
import { useViews } from './hooks/useViews';
import { RouletteGUI } from './components/RouletteGUI';
import { LobbyButton } from './RNGRoulette/components/Buttons/LobbyButton';
import { mapBettingOptionsToBetAreas } from './utils/mapBettingOptionsToBetAreas';
import { RoundResult } from './components/RoundResult';
import { Table } from './components/Table';
import { MultipliersTable } from './components/MultipliersTable';
import { FrenchTable } from './components/FrenchTable';
import { IHighlighted } from './components/Table/Table';
import { useBetsUpdates } from './hooks/useBetsUpdates';
import { PartnerLogo } from './components/PartnerLogo';
import { StatisticsModal } from './components/Modals/StatisticsModal';
import { RouletteMultipliers } from './RouletteFlashLT/components/RouletteMultipliers';
import { SHOW_TABLE_MULTIPLIERS_TIMEOUT } from './constants';
import classes from './RouletteContainer.module.scss';

interface IRouletteContainerProps {
    gameId: ILotteryGameId;
    gameRouletteStore: GameRouletteStore;
    showCurtain: boolean;
    renderBackground?(): JSX.Element;
    tips?: boolean;
    multipliers?: IRouletteMultipliersData;
    canShowGuiRoulette?: boolean;
}

const ROULETTE_CASINO_OPTIONS_CLASSES = Object.values(RouletteOptionsClasses);

export const RouletteContainer = (
    props: PropsWithChildren<IRouletteContainerProps>,
): JSX.Element => {
    const gameData = useStore(props.gameRouletteStore, gameRouletteSelectors.selectData);
    const bettingOptions = useStore(
        gamesOptionsEntity.store(props.gameId),
        gamesOptionsSelectors.getOptions,
    );
    const isLandscape = useLandscape();
    const topNotification = useTopNotification(useCancelDraw(props.gameId, gameData.runId));
    const message = useStore(gamesMessagesEntity.store(GameMessagePlacement.CENTER));
    const isGuiMode = useStore(playerSettingsStore, playerSettingsSelectors.getGuiMode);
    const authorized = useStore(authStore);
    const gameStatusStore = gamesStatusEntity.store(props.gameId);
    const openForBets = useStore(gameStatusStore, gamesStatusSelectors.getOpenForBets);
    const started = useStore(gameStatusStore, gamesStatusSelectors.getStarted);
    const isMaintenance = useStore(gameStatusStore, gamesStatusSelectors.isMaintenance);
    const casinoLoading = useCasinoBetting({
        gameId: props.gameId,
        runId: gameData.runId,
        runOpened: openForBets,
        optionsClasses: ROULETTE_CASINO_OPTIONS_CLASSES,
        validationServiceFactory: () => new RouletteValidationService(props.gameId, bettingOptions),
    });
    const { isMobileView, isTabletView, isDesktopView } = useViews();
    const [isFrenchTable, setIsFrenchTable] = useState<boolean>(false);
    const [collapsed, setCollapsed] = useState<boolean>(false);
    const showCollapseButton =
        partnerSettingsStore.isCollapsibleTable &&
        !GameDefinition.isRng(props.gameId) &&
        isMobileView &&
        openForBets;
    const showLayeredContent = props.showCurtain && isMobileView;
    const results = useLastResultsList(props.gameId);
    const [delayedLastResult, setDelayedLastResult] = useState(results[0]);
    const displayedLastResult = isGuiMode ? delayedLastResult : results[0];
    const showClassicTable = (!isDesktopView && !isFrenchTable) || isDesktopView;
    const showFrenchTable = (!isDesktopView && isFrenchTable) || isDesktopView;
    const [remoteHighlighted, setRemoteHighlighted] = useState<IHighlighted>([]);
    const groupedOptions = useMemo(() => keyBy(gameData.bettingOptions, 'oddsClass'), []);

    const groupedGameItems = useMemo(() => keyBy(gameData.gameItems, 'number'), []);
    const { partnerSettings } = partnerSettingsStore;
    const groupedByIdGameItems = useMemo(() => keyBy(gameData.gameItems, 'id'), []) as Record<
        number,
        ILotteryItem
    >;
    const showGUIRoulette =
        (typeof props.canShowGuiRoulette !== 'boolean' || props.canShowGuiRoulette) &&
        isGuiMode &&
        started;

    const showPartnerLogo = isDesktopView || (isGuiMode && !showGUIRoulette && !openForBets);

    const prevOptionItemId = useRef<string>();

    useAutoPlay({ gameId: props.gameId });
    useBetsUpdates(props.gameId);
    usePayoutModal(props.gameId, isMaintenance, bettingOptions);

    const onChipDrag = !uaParser.isTouchDevice
        ? undefined
        : (optionItemId: string) => {
              if (!optionItemId || optionItemId === prevOptionItemId.current) {
                  return;
              }

              prevOptionItemId.current = optionItemId;

              setRemoteHighlighted(
                  mapBettingOptionsToBetAreas(
                      optionItemId,
                      gameData.bettingOptions,
                      groupedByIdGameItems,
                  ),
              );
          };

    const runResultValue =
        Number(displayedLastResult?.runId) === gameData.runId
            ? Number(displayedLastResult?.items[0].number)
            : undefined;

    const switchTableView = (): void => {
        setIsFrenchTable((value) => {
            analyticsApi.trackRouletteTableSwitch(props.gameId, !value);
            return !value;
        });

        CasinoServiceFactory.getValidation(props.gameId).resetErrorTimeout();
    };

    const { otherGamesHaveBets } = useAnyBetsPlaced();

    useStaticHeightGame({ staticHeightCallback: getIframeHeightToFitVideo });

    useAfterUpdate(() => {
        if (!openForBets) {
            casinoDraftBetAmountsEntity.store(props.gameId).clear();
            setCollapsed(false);
        }
    }, [openForBets]);

    useEffect(() => {
        const timeoutId = setTimeout(() => {
            setDelayedLastResult(results[0]);
        }, ROULETTE_LAST_RESULT_DELAY_FOR_GUI);

        return () => {
            clearTimeout(timeoutId);
        };
    }, [results[0]?.runId]);

    const [showMultiplier, setShowMultiplier] = useState(false);

    console.log('test');

    useEffect(() => {
        let timeout: number;

        if (!openForBets) {
            setTimeout(() => {
                setShowMultiplier(true);
            }, SHOW_TABLE_MULTIPLIERS_TIMEOUT);
        }

        setShowMultiplier(false);

        return () => {
            clearTimeout(timeout);
        };
    }, [openForBets]);

    if (casinoLoading) {
        return <InitialPageSkeleton />;
    }

    const isAutoPlayEnabled = partnerSettingsStore.isAutoPlayEnabled(props.gameId);

    const setAutoPlayDraftChips = (autoPlayDraftChips: IChipAmounts): void => {
        setRemoteHighlighted(
            uniq(
                Object.keys(autoPlayDraftChips).reduce<IHighlighted>(
                    (acc, optionItemId) =>
                        acc.concat(
                            mapBettingOptionsToBetAreas(
                                optionItemId,
                                gameData.bettingOptions,
                                groupedByIdGameItems,
                            ),
                        ),
                    [],
                ),
            ),
        );
    };

    return (
        <GameContent>
            <RotateRestriction forced={isLandscape} />
            <GameInfo className={classes.notifications}>
                <TopNotifications message={topNotification} />
                {!!message && (
                    <GameMessages
                        type={message?.type}
                        message={message?.message}
                        className={classes.gameMessages}
                    />
                )}
                <PlayerWonMessage
                    gameId={props.gameId}
                    className={classes.playerWonMessage}
                    delay={isGuiMode ? ROULETTE_LAST_RESULT_DELAY_FOR_GUI : 0}
                />
            </GameInfo>

            {!authorized && <LoginMessage className={classes.gameMessages} />}
            {isMaintenance && (
                <div className={classes.guiMaintenanceTimer}>
                    <Clock />
                    <GameTimer gameId={props.gameId} isMaintenance={isMaintenance} />
                </div>
            )}
            {!isMobileView && (
                <>
                    {!isMaintenance && (
                        <LiveInfo gameId={props.gameId} drawCode={gameData.drawCode} />
                    )}
                    <GamePanel className={classes.gamePanel} fluidFont={false}>
                        <ButtonsList>
                            <ChatButton gameId={props.gameId} />
                            {isTabletView && (
                                <TableButton onClick={switchTableView} active={isFrenchTable} />
                            )}
                            {GameDefinition.isRng(props.gameId) && <LobbyButton />}
                            <StatisticsButton
                                groupedOptions={groupedOptions}
                                groupedGameItems={groupedGameItems}
                                setRemoteHighlighted={setRemoteHighlighted}
                                gameId={props.gameId}
                                ModalComponent={StatisticsModal}
                            />
                            <SoundButton />
                            <VideoButton />
                        </ButtonsList>
                    </GamePanel>
                </>
            )}
            {showGUIRoulette && (
                <RouletteGUI
                    gameId={props.gameId}
                    gameRouletteStore={props.gameRouletteStore}
                    renderBackground={props.renderBackground}
                />
            )}

            <AdaptiveContainer className={classes.adaptiveContainer}>
                <Curtain
                    gameId={props.gameId}
                    className={classNames({
                        hidden: !props.showCurtain,
                        [classes.backdropLayer]:
                            showLayeredContent && GameDefinition.isRng(props.gameId),
                    })}
                />
                <div className={classNames(classes.contentLayer, classes.zIndex)}>
                    <LastResults gameId={props.gameId} results={results} />

                    {props.children}

                    {showPartnerLogo && (
                        <PartnerLogo gameId={props.gameId} isDesktop={isDesktopView} />
                    )}

                    <CasinoContainer gameId={props.gameId} onChipDrag={onChipDrag}>
                        {({ onChipPointerUp, onChipPointerDown, addChips }) => (
                            <>
                                {otherGamesHaveBets && (
                                    <Message
                                        className={classes.restrictBetsMessage}
                                        type={GameMessageType.INFO}
                                        text={t.string('active_bet_is_settled')}
                                    />
                                )}
                                {showClassicTable && (
                                    <>
                                        <Table
                                            collapsed={collapsed}
                                            disabled={!openForBets || otherGamesHaveBets}
                                            gameId={props.gameId}
                                            remoteHighlighted={remoteHighlighted}
                                            groupedOptions={groupedOptions}
                                            groupedGameItems={groupedGameItems}
                                            openForBets={openForBets}
                                            addChips={addChips}
                                            onChipPointerDown={onChipPointerDown}
                                            onChipPointerUp={onChipPointerUp}
                                            result={runResultValue}
                                            key={`classic-${isMobileView}`}
                                            multipliers={showMultiplier && props.multipliers}
                                        />
                                        {!openForBets &&
                                            props.gameId === GameId.ROULETTE_FLASH_LT && (
                                                <MultipliersTable
                                                    result={runResultValue}
                                                    key={`classic-multipliers-${isMobileView}`}
                                                    multipliers={
                                                        showMultiplier && props.multipliers
                                                    }
                                                />
                                            )}
                                    </>
                                )}
                                {showFrenchTable && (
                                    <FrenchTable
                                        collapsed={collapsed}
                                        disabled={!openForBets || otherGamesHaveBets}
                                        gameId={props.gameId}
                                        groupedOptions={groupedOptions}
                                        groupedGameItems={groupedGameItems}
                                        setRemoteHighlighted={setRemoteHighlighted}
                                        addChips={addChips}
                                        openForBets={openForBets}
                                        result={runResultValue}
                                        key={`french-${isMobileView}`}
                                        multipliers={
                                            showMultiplier && !isDesktopView
                                                ? props.multipliers
                                                : undefined
                                        }
                                    />
                                )}

                                {!!props.multipliers?.items.length && (
                                    <RouletteMultipliers
                                        results={results}
                                        multipliers={props.multipliers}
                                    />
                                )}
                            </>
                        )}
                    </CasinoContainer>

                    {!!displayedLastResult && (
                        <RoundResult
                            gameId={props.gameId}
                            result={displayedLastResult}
                            currentRunId={gameData.runId}
                        />
                    )}

                    {isMobileView ? (
                        <>
                            {!isMaintenance && (
                                <LiveInfoMobile
                                    gameId={props.gameId}
                                    drawCode={gameData.drawCode}
                                />
                            )}
                            <PortraitRightPanel
                                className={classNames(
                                    classes.smallHeight,
                                    classes.portraitRightPanel,
                                )}
                            >
                                <ButtonsList direction="column">
                                    <FullscreenButton />
                                    <SoundButton />
                                    {showCollapseButton ? (
                                        <CollapseButton
                                            onClick={() => setCollapsed(!collapsed)}
                                            collapsed={collapsed}
                                        />
                                    ) : (
                                        <VideoButton />
                                    )}
                                </ButtonsList>
                                <Chips
                                    gameId={props.gameId}
                                    openForBets={openForBets}
                                    align="right"
                                    className={classes.chipsPortrait}
                                />
                                <ButtonsList direction="column">
                                    {isAutoPlayEnabled && (
                                        <AutoPlayButton
                                            gameId={props.gameId}
                                            openForBets={openForBets}
                                            setDraftChips={setAutoPlayDraftChips}
                                        />
                                    )}
                                    <TableButton onClick={switchTableView} active={isFrenchTable} />
                                    <StatisticsButton
                                        groupedOptions={groupedOptions}
                                        groupedGameItems={groupedGameItems}
                                        setRemoteHighlighted={setRemoteHighlighted}
                                        gameId={props.gameId}
                                        ModalComponent={StatisticsModal}
                                    />
                                    {GameDefinition.isRng(props.gameId) && <LobbyButton />}
                                    <ChatButton gameId={props.gameId} />
                                </ButtonsList>
                            </PortraitRightPanel>
                            {authorized && (
                                <PlayerStatsFooter
                                    gameId={props.gameId}
                                    variableName="--roulette-footer-height"
                                />
                            )}
                        </>
                    ) : (
                        <>
                            {authorized && (
                                <>
                                    {props.tips && (
                                        <TipsButton
                                            gameId={props.gameId}
                                            className={classes.tipsDesktop}
                                            runId={gameData.runId}
                                        />
                                    )}
                                    <PlayerStats
                                        gameId={props.gameId}
                                        className={classes.playerStats}
                                    />
                                </>
                            )}
                            <div className={classes.chipsLandscapeContainer}>
                                <Chips
                                    gameId={props.gameId}
                                    openForBets={openForBets}
                                    className={classNames(classes.chipsLandscape, {
                                        [classes.disabled]: !openForBets,
                                    })}
                                    chipsLineClassName={classes.chipsLine}
                                />
                                {isAutoPlayEnabled && (
                                    <AutoPlayButton
                                        gameId={props.gameId}
                                        openForBets={openForBets}
                                        className={classNames(classes.autoplayLandscape, {
                                            [classes.showClassicTable]: showClassicTable,
                                            [classes.drawStarted]: !openForBets,
                                        })}
                                        buttonClassName={classes.autoplayButtonLandscape}
                                        setDraftChips={setAutoPlayDraftChips}
                                    />
                                )}
                            </div>
                        </>
                    )}
                </div>
                <RoundInfoContainer
                    className={classNames(classes.smallHeight, {
                        [classes.timerWithPromotions]: isMobileView,
                    })}
                >
                    {authorized && !isMaintenance && (
                        <>
                            <RoundTimer
                                gameId={props.gameId}
                                key={gameData.runId}
                                className={classes.roundTimer}
                            />
                            {props.tips && isMobileView && (
                                <TipsButton
                                    gameId={props.gameId}
                                    className={classes.tipsMobile}
                                    runId={gameData.runId}
                                />
                            )}
                        </>
                    )}
                    {isMobileView && partnerSettings.isNotificationEnabled && (
                        <div
                            className={classNames(classes.promotionsContainer, {
                                [classes.hiddenTimer]: !openForBets,
                            })}
                        >
                            <Promotions
                                panelClassName={classes.promotionsPanel}
                                className={classes.promotionsItems}
                                gameId={props.gameId}
                            />
                        </div>
                    )}
                </RoundInfoContainer>
            </AdaptiveContainer>
            <div
                className={classNames(classes.tableGradient, {
                    [classes.drawStarted]: !openForBets && !isMobileView,
                })}
            />
        </GameContent>
    );
};
