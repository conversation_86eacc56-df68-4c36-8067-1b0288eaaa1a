import { JSX, useEffect, useState } from 'react';
import {
    GAME_STOP_BETTING_OFFSET,
    GameId,
    gamesOptionsEntity,
    gamesRunsEntity,
    gamesStatusService,
    mapToGroupsIds,
    sessionStore,
    useStaticHeightGame,
} from '@bg-shared';
import { keyBy } from 'lodash-unified';
import { InitialPageSkeleton } from '@bg-components/Skeleton';
import { gameEvents } from './business/services/Game.events';
import { gameApi } from './business/services/Game.api';
import { gameBonusStore } from './business/modules/GameBonus';
import { currentSectorsStore } from './business/modules/CurrentSectors';
import { GizaContent } from './GizaContent';
import { lastResultsStore } from './business/modules/LastResults';

const Giza = (): JSX.Element => {
    const [loading, setLoading] = useState<boolean>(true);

    useStaticHeightGame();

    console.log('test');

    useEffect(() => {
        let unsubscribeFromGameEvents: Array<() => void> = [];

        Promise.all([
            gameApi.getGizaInitial(sessionStore.partnerCode),
            gameApi.getBettingOptions(sessionStore.partnerCode),
        ])
            .then(([initial, bettingOptions]) => {
                gamesRunsEntity.store(GameId.GIZA).update({
                    run: {
                        id: initial.run.id,
                        code: initial.run.code,
                    },
                    round: {
                        id: initial.run.runRoundId,
                        number: initial.run.roundNumber,
                    },
                });

                gamesOptionsEntity.store(GameId.GIZA).update({
                    options: keyBy(
                        bettingOptions.groups.map((group) => group.options).flat(),
                        'id',
                    ),
                    groups: mapToGroupsIds(bettingOptions.groups),
                    items: bettingOptions.items,
                });
                if (initial.bonus) {
                    gameBonusStore.update(initial.bonus);
                }

                currentSectorsStore.update(initial.currentSectorValues);
                lastResultsStore.update(initial.lastResults);

                gamesStatusService.configureTimer({
                    gameId: GameId.GIZA,
                    seconds: initial.bonus?.round?.secondsLeft ?? initial.run.secondsLeft,
                    timestamp: (initial.bonus?.round?.infoTime ?? initial.run.infoTime) / 1000,
                    offsetSeconds: GAME_STOP_BETTING_OFFSET,
                });

                unsubscribeFromGameEvents = gameEvents.configureEvents();

                setLoading(false);
            })
            .catch((e) => {
                console.error(e);
            });

        return () => {
            unsubscribeFromGameEvents.forEach((unsubscribe) => unsubscribe());
        };
    }, []);

    if (loading) {
        return <InitialPageSkeleton />;
    }

    return <GizaContent />;
};

export default Giza;
