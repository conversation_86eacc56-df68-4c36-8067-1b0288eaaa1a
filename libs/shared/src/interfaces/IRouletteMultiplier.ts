import { FlashRouletteRunType, InsideBetColor } from '@bg-shared/enums';

export interface IRouletteMultiplierResult {
    runType: FlashRouletteRunType;
    itemId: number;
    number: number;
    color: string;
    multiplier: number;
    isPlatinum: boolean;
    multipliers: IRouletteMultiplier[];
}

export interface IRouletteMultiplier {
    number: number;
    multiplier: number;
    itemId: number;
    isPlatinum: boolean;
    color?: InsideBetColor;
}

export interface IRouletteMultiplierModel {
    runId: number;
    runType: FlashRouletteRunType;
    multipliers: IRouletteMultiplier[];
}

export interface IRouletteMultipliersData {
    runType: FlashRouletteRunType;
    items: IRouletteMultiplier[];
}
