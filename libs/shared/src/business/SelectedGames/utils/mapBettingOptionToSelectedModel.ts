import { ISelectedBettingOption } from '@bg-shared/interfaces/IBettingOption';
import { ISelectedGameModel } from '../interfaces';

export const mapBettingOptionToSelectedModel = (
    option: ISelectedBettingOption,
    isBatchItem?: boolean,
): ISelectedGameModel => ({
    optionId: option.id,
    optionClass: option.oddsClass,
    gameId: option.gameId,
    value: option.value,
    items: option.items,
    random: !!option.random,
    multipliers: option.multipliers,
    isBatchItem,
});
