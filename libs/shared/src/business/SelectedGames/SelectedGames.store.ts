import { Store } from '@betgames/bg-state-manager';
import { ClassicGameDefinition } from '@bg-shared/utils/ClassicGameDefinition';
import { flatten, sample } from 'lodash-unified';
import { StorageRepository, setImmediateTimeout } from '@betgames/bg-tools';
import { SELECTED_GAMES_KEY } from '@bg-shared/constants/storageKeys';
import PartnerSettingsStore from '@bg-shared/business/PartnerSettings/PartnerSettings.store';
import SessionStore from '@bg-shared/business/Session/Session.store';
import { GameId } from '@bg-shared/enums/GameId';
import { IBetslipGameState } from '@bg-shared/contexts/Games/interfaces/IBetslipGames';
import { MAX_COMBO_ODDS_COUNT } from '@bg-shared/constants/constants';
import { ILotteryGameId } from '@bg-shared/interfaces/GameId';
import { BettingOptionStatus } from '@bg-shared/enums/BettingOptionStatus';
import { ISelectedBettingOption } from '@bg-shared/interfaces/IBettingOption';
import { mapBettingOptionToSelectedModel } from '@bg-shared/business/SelectedGames/utils/mapBettingOptionToSelectedModel';
import { ISelectedGameModel, IGamesStorageModel } from './interfaces';

const RESET_STORAGE_TIMEOUT_SECONDS = 3 * 60 * 60; // 3h

export default class SelectedGamesStore extends Store<ISelectedGameModel[]> {
    constructor(
        private readonly partnerSettingsStore: PartnerSettingsStore,
        private readonly sessionStore: SessionStore,
        private readonly storageRepository: StorageRepository,
    ) {
        super({
            key: 'SelectedGames',
            default: [],
        });

        this.update(this.getFromStorage());
        this.subscribe((games) => {
            this.storageRepository.setItemParsed(SELECTED_GAMES_KEY, {
                timestamp: Date.now(),
                games,
            });
        });
    }

    private getFromStorage(): ISelectedGameModel[] {
        if (this.sessionStore.singleGame) {
            return [];
        }

        const storage =
            this.storageRepository.getItemParsed<IGamesStorageModel>(SELECTED_GAMES_KEY);

        if (
            !storage ||
            !storage.games.length ||
            (Date.now() - storage.timestamp) / 1000 > RESET_STORAGE_TIMEOUT_SECONDS
        ) {
            return [];
        }

        return storage.games.filter((game) =>
            this.partnerSettingsStore.availableGamesIds.includes(game.gameId),
        );
    }

    private updateGames(games: ISelectedGameModel[]): void {
        setImmediateTimeout(() => {
            this.update(games);
        });
    }

    public add(option: ISelectedGameModel): void {
        const currentGames = [...this.value].filter((item) => !item.isBatchItem);
        const batchSelectionGames = [...this.value].filter((item) => item.isBatchItem);
        const temp: ISelectedGameModel[] = [];

        const allSelectedGamesAvailableForCombination = currentGames.every((odd) =>
            ClassicGameDefinition.isWithCombinationOdds(odd.gameId),
        );

        if (
            this.partnerSettingsStore.partnerSettings.isCombinationEnabled &&
            ClassicGameDefinition.isWithCombinationOdds(option.gameId) &&
            allSelectedGamesAvailableForCombination
        ) {
            temp.push(...currentGames);
        }

        const sameGameOddIndex = currentGames.findIndex((odd) => odd.gameId === option.gameId);

        if (sameGameOddIndex !== -1) {
            temp[sameGameOddIndex] = option;
        } else if (temp.length === MAX_COMBO_ODDS_COUNT) {
            temp.pop();
            temp.unshift(option);
        } else {
            temp.push(option);
        }

        currentGames.length = 0;
        currentGames.push(...temp, ...batchSelectionGames);

        this.updateGames(currentGames);
    }

    public addBatch(options: ISelectedGameModel[]): void {
        this.updateGames([...this.value, ...options]);
    }

    public addRandom(
        gamesIds: GameId[],
        games: Record<GameId, IBetslipGameState>,
        topOptions?: Record<GameId, number[]>,
    ): void {
        const currentGames = [...this.value];
        const filteredIds = gamesIds.filter((gameId) =>
            ClassicGameDefinition.isWithRandomOdds(gameId),
        );

        let filteredOptions = flatten(
            filteredIds.map((id) =>
                Object.values(games[id].data.bettingOptions).filter((option) => {
                    const isTop = topOptions?.[id as ILotteryGameId]?.includes(option.id) ?? true;
                    const isActive = option.status === BettingOptionStatus.ACTIVE;
                    const withoutItems = !option.itemsCount;

                    return isActive && isTop && withoutItems;
                }),
            ),
        );

        if (!filteredOptions.length) {
            filteredOptions = flatten(
                filteredIds.map((id) =>
                    Object.values(games[id].data.bettingOptions).filter(
                        (option) => !option.itemsCount,
                    ),
                ),
            );
        }

        const selectedOptionsIds = currentGames.map((option) => option.optionId);
        let randomOption: ISelectedBettingOption;

        while (!randomOption || selectedOptionsIds.includes(randomOption?.id)) {
            randomOption = sample(filteredOptions);
        }

        currentGames.push({
            ...mapBettingOptionToSelectedModel(randomOption),
            random: true,
        });

        this.updateGames(currentGames);
    }

    public remove(id: number): void {
        this.updateGames(this.value.filter((game) => game.optionId !== id));
    }

    public removeFromBatch(gameId: GameId, index: number): void {
        const currentGames = this.value.filter((game) => game.gameId !== gameId);
        const gameOptions = this.value.filter((game) => game.gameId === gameId);

        gameOptions.splice(index, 1);
        if (gameOptions.length) {
            currentGames.push(...gameOptions);
        }

        this.updateGames(currentGames);
    }

    public truncate(gameId: GameId): void {
        this.updateGames(this.value.filter((game) => game.gameId === gameId));
    }

    public reset(isBatch?: boolean): void {
        this.updateGames(
            this.value.filter((item) => (isBatch ? !item.isBatchItem : item.isBatchItem)),
        );
    }
}
