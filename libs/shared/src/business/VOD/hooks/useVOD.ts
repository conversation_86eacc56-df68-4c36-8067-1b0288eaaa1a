import { useState, useRef, useEffect } from 'react';
import { useMounted } from '@betgames/bg-tools';
import { GameId } from '@bg-shared/enums/GameId';
import { vodService } from '../VOD.service';
import { IVideoReadyEvent } from '../interfaces';

export const useVOD = (gameId: GameId): IVideoReadyEvent => {
    const [eventData, setEventData] = useState<IVideoReadyEvent>();
    const runCodeRef = useRef<string>();
    const mounted = useMounted();

    useEffect(() => {
        // Get latest video ready event for VOD games
        const unsubscribe = vodService.subscribe(gameId, (response) => {
            if (!mounted.current) {
                return;
            }

            if (response.runCode !== runCodeRef.current) {
                setEventData(response);
                runCodeRef.current = response.runCode;
            }
        });

        return () => {
            unsubscribe();
        };
    }, []);

    return eventData;
};
