import { Store } from '@betgames/bg-state-manager';
import { GameId } from '@bg-shared/enums';
import { IChipAmounts } from '@bg-shared/business/CasinoBetting/interfaces';
import { ClassicGameDefinition } from '@bg-shared/utils/ClassicGameDefinition';
import { StagedBetsApi } from './StagedBets.api';
import { IStagedBetsStore } from './interfaces';

export class StagedBetsStore extends Store<IStagedBetsStore> {
    constructor(private readonly stagedBetsApi: StagedBetsApi) {
        super({
            key: 'StagedBets',
            default: {} as IStagedBetsStore,
        });
    }

    public updateFromCasinoChips(gameId: GameId, data: IChipAmounts) {
        this.update((draft) => {
            draft[gameId] = Object.keys(data).map((oddId) => ({
                oddId,
                amount: data[oddId],
            }));
        });
    }

    public updateFromAPI() {
        this.stagedBetsApi
            .getStagedBets(ClassicGameDefinition.GAMES_WITH_CHIPS)
            .then((response) => {
                const formattedOdds = response.data.reduce<IStagedBetsStore>((acc, bet) => {
                    acc[bet.gameId] = [...bet.stagedBets.bets];

                    return acc;
                }, {} as IStagedBetsStore);

                this.update(formattedOdds);
            })
            .catch(() => {});
    }
}
