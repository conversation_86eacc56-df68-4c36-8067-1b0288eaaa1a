import { Store } from '@betgames/bg-state-manager';
import { IQualities } from '@bg-shared/interfaces/IVideoQualities';
import { IVideoStreamModel } from './interfaces';

export default class VideoStreamStore extends Store<IVideoStreamModel> {
    constructor() {
        super({
            key: 'VideoStream',
            default: {
                ready: false,
                timeToReady: 0,
                playing: false,
                qualities: null,
                selectedQuality: null,
                startTime: 0,
            },
        });
    }

    public set qualities(qualities: IQualities) {
        this.update((draft) => {
            draft.qualities = qualities;
        });
    }

    public get qualities() {
        return this.value.qualities;
    }

    public set selectedQuality(quality: string) {
        this.update((draft) => {
            draft.selectedQuality = quality;
        });
    }

    public get selectedQuality() {
        return this.value.selectedQuality;
    }

    public set ready(value: boolean) {
        this.update((draft) => {
            draft.ready = value;
        });
    }

    public get ready() {
        return this.value.ready;
    }

    public set playing(value: boolean) {
        this.update((draft) => {
            draft.playing = value;
        });
    }

    public get playing() {
        return this.value.playing;
    }

    public set startTime(value: number) {
        this.update((draft) => {
            draft.startTime = value;
        });
    }

    public get startTime() {
        return this.value.startTime;
    }

    public set timeToReady(value: number) {
        this.update((draft) => {
            draft.timeToReady = value;
        });
    }

    public get timeToReady() {
        return this.value.timeToReady;
    }
}

export const videoStreamStore = new VideoStreamStore();
