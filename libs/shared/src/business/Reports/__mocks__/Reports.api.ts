import { forceCast } from '@betgames/bg-tools';
import { GameId } from '@bg-shared/enums/GameId';
import {
    IFetchResultsParams,
    IResultsPage,
    IResultsPageItem,
} from '@bg-shared/business/Reports/interfaces_legacy/ResultsReport';
import { IGenericBet } from '@bg-shared/business/Reports/interfaces_legacy/Bets';
import { BetType } from '@bg-shared/enums/BetType';
import {
    IBetHistoryParams,
    IBetHistoryResponse,
    IBetHistorySubscriptionLegacy,
} from '../interfaces';

export default class ReportsApi {
    public fetchResults = (
        partnerCode: string,
        params: IFetchResultsParams,
    ): Promise<IResultsPage> =>
        new Promise(async (resolve, reject) => {
            if (params.timezone === 111) {
                return reject(new Error());
            }

            if (params.page === '2') {
                const value = await import('./fixture/results.page2.json');
                return resolve(value.default as IResultsPage);
            }

            import('./fixture/results.json').then((value) => {
                resolve(value.default as IResultsPage);
            });
        });

    public fetchSingleResult = (partnerCode: string, runCode: string): Promise<IResultsPageItem> =>
        import('./fixture/result.lucky7.json').then((value) => {
            if (runCode === '-1') {
                throw Error();
            }

            if (runCode === '-2') {
                return null;
            }

            return value.default;
        });

    public fetchBetHistory = (params: IBetHistoryParams): Promise<IBetHistoryResponse> =>
        new Promise((resolve, reject) => {
            if (params.timezone === 111) {
                return reject(new Error());
            }

            import('./fixture/history.formatted.json').then((value) => {
                resolve(forceCast<IBetHistoryResponse>(value.default));
            });
        });

    public fetchBetRecord = (params: {
        id: number;
        type: BetType;
    }): Promise<IBetHistorySubscriptionLegacy> =>
        import('./fixture/history.record.json').then((value) => {
            if (params.id === -1) {
                throw Error();
            }

            return forceCast<IBetHistorySubscriptionLegacy>(value.default);
        });

    public fetchRecentBets = (limit: number): Promise<IGenericBet[]> =>
        Promise.resolve({} as IGenericBet[]);

    public fetchRecentBetsPerRun = (gameId: GameId, runId: number): Promise<IGenericBet[]> =>
        Promise.resolve([] as IGenericBet[]);
}

export const reportsApi = new ReportsApi();
