import { isNil, omitBy } from 'lodash-unified';
import { BettingOptionsFormat } from '@betgames/bg-tools';
import { GameId } from '@bg-shared/enums/GameId';
import {
    DEFAULT_LANGUAGE,
    DEFAULT_PARTNER_TOKEN,
    DEFAULT_TIMEZONE,
} from '@bg-shared/constants/constants';
import {
    DATE_FORMAT_BY_COUNTRY,
    TIME_FORMAT_BY_COUNTRY,
    TIME_FULL_FORMAT_BY_COUNTRY,
    DATE_TIME_FORMAT_BY_COUNTRY,
    DEFAULT_DATE_FORMAT_KEY,
} from '@bg-shared/constants/dateFormats';
import {
    IAuthResponse,
    IReverseAuthResponse,
    IAuthValidatedParams,
} from '@bg-shared/business/Auth/interfaces';
import { getLocalTimezone } from '@bg-shared/utils/getLocalTimezone';
import { ICurrency } from '@bg-shared/interfaces/ICurrency';
import { ISession } from './interfaces';

export default class SessionStore {
    private state: ISession = {
        partnerCode: '-',
        partnerToken: DEFAULT_PARTNER_TOKEN,
        oddsFormat: BettingOptionsFormat.DECIMAL,
        timezone: DEFAULT_TIMEZONE,
        language: DEFAULT_LANGUAGE,
        playerId: 0,
        webSocketsToken: null,
        skywardDeluxeWebSocketsToken: null,
        apiUrl: null,
        partnerUrl: null,
        newIntegration: false,
        scriptIntegration: false,
        nickname: null,
        lobbyLiveGames: false,
        lobbyDefaultTab: 0,
    };

    public update(session: Partial<ISession>): void {
        this.state = {
            ...this.state,
            ...session,
        };
    }

    public configure(
        response: Partial<IAuthResponse | IReverseAuthResponse>,
        params?: IAuthValidatedParams,
    ): void {
        this.update(
            omitBy<ISession>(
                {
                    authToken: response?.auth,
                    playerId: response?.playerId,
                    webSocketsToken: response?.webSocketsToken,
                    skywardDeluxeWebSocketsToken: response?.skywardDeluxeToken,
                    apiUrl: params?.apiUrl,
                    nickname: response.nickname,
                    partnerCode:
                        (response as IReverseAuthResponse)?.partnerCode ?? params?.partnerCode,
                    partnerToken: params?.partnerToken ?? '-',
                    language:
                        (response as IReverseAuthResponse)?.language ??
                        params?.language ??
                        DEFAULT_LANGUAGE,
                    timezone: params?.timezone ?? getLocalTimezone(),
                    oddsFormat: params?.oddsFormat ?? BettingOptionsFormat.DECIMAL,
                    homeUrl: (response as IReverseAuthResponse)?.homeUrl ?? params?.homeUrl,
                    partnerUrl: params?.partnerUrl,
                    bettingAmounts: params?.bettingAmounts ?? response?.betAmounts?.map(parseFloat),
                    chipAmounts: response?.chipAmounts?.map(parseFloat),
                    defaultChipAmount: response?.defaultChipAmount
                        ? Number(response.defaultChipAmount)
                        : undefined,
                    currency: response?.currency,
                    sid: params?.sid,
                    singleGame: params?.singleGame,
                    themeEditor: params?.themeEditor,
                    onlyGUI: params?.onlyGUI,
                    defaultGUI: params?.defaultGUI,
                    newIntegration: params?.newIntegration,
                    scriptIntegration: params?.scriptIntegration,
                    defaultGame: (response as IReverseAuthResponse)?.gameId ?? params?.defaultGame,
                    dateTimeFormat: params?.dateTimeFormat ?? DEFAULT_DATE_FORMAT_KEY,
                    wsUrl: params?.wsUrl,
                    scrolling: !!Number(params?.scrolling),
                    lobbyLiveGames: params?.lobbyLiveGames || false,
                    lobbyDefaultTab: params?.lobbyDefaultTab || 0,
                    rgs: params?.rgs,
                },
                isNil,
            ),
        );
    }

    public clearToken(): void {
        this.update({
            authToken: DEFAULT_PARTNER_TOKEN,
        });
    }

    public get session(): ISession {
        return this.state;
    }

    public get timezone(): number {
        return this.state.timezone;
    }

    public get authToken(): string {
        return this.state.authToken;
    }

    public get oddsFormat(): BettingOptionsFormat {
        return this.state.oddsFormat;
    }

    public get partnerCode(): string {
        return this.state.partnerCode;
    }

    public get bettingAmounts(): number[] {
        return this.state.bettingAmounts;
    }

    public get chipAmounts(): number[] {
        return this.state.chipAmounts || [0.1, 0.25, 0.55, 1, 2, 5, 10, 25];
    }

    public get defaultChipAmount(): number {
        return this.state.defaultChipAmount;
    }

    public get homeUrl(): string {
        return this.state.homeUrl;
    }

    public get onlyGUI(): boolean | undefined {
        return this.state.onlyGUI;
    }

    public get defaultGUI(): boolean | undefined {
        return this.state.defaultGUI;
    }

    public get defaultGame(): number | undefined {
        return this.state.defaultGame;
    }

    public get language(): string {
        return this.state.language;
    }

    public get playerId(): number {
        return this.state.playerId;
    }

    public get apiUrl(): string {
        return this.state.apiUrl;
    }

    public get webSocketsToken(): string {
        return this.state.webSocketsToken;
    }

    public get skywardDeluxeWebSocketsToken(): string {
        return this.state.skywardDeluxeWebSocketsToken;
    }

    public get webSocketsUrl(): string {
        return this.state.wsUrl;
    }

    public get singleGame(): GameId | undefined {
        return this.state.singleGame;
    }

    public get dateFormat(): string {
        return DATE_FORMAT_BY_COUNTRY[this.state.dateTimeFormat];
    }

    public get timeFormat(): string {
        return TIME_FORMAT_BY_COUNTRY[this.state.dateTimeFormat];
    }

    public get timeFullFormat(): string {
        return TIME_FULL_FORMAT_BY_COUNTRY[this.state.dateTimeFormat];
    }

    public get dateTimeFormat(): string {
        return DATE_TIME_FORMAT_BY_COUNTRY[this.state.dateTimeFormat];
    }

    public get scrolling(): boolean {
        return this.state.scrolling;
    }

    public get nickname(): string {
        return this.state.nickname;
    }

    public get currency(): ICurrency {
        return this.state.currency;
    }

    public get lobbyLiveGames(): boolean | undefined {
        return this.state.lobbyLiveGames;
    }

    public get lobbyDefaultTab(): number {
        return this.state.lobbyDefaultTab;
    }
}

export const sessionStore = new SessionStore();
