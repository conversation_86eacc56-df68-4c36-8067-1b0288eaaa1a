import { IGenericBet } from '@bg-shared/business/Reports/interfaces_legacy/Bets';
import { getBetType } from '@bg-shared/utils';
import { BetStatus } from '@bg-shared/enums/BetStatus';

export const getDelay = (
    currentState: IGenericBet[],
    bet: IGenericBet,
    delays: Record<string, number>,
): number => {
    const currentBet = currentState.find((item) => item.id === bet.id);
    /**
     * New recent bet items can be created only after placing a bet,
     * no need for delay, it should appear immediately after place bet action
     */
    if (!currentBet) {
        return 0;
    }

    if (getBetType.isCombination(bet)) {
        /**
         * Delay value is depend on the game. Combo can include multiple games
         * were both can have different LL or HLS delays.
         * We need to find bet, which in current state has active status, but not anymore
         * active in the new data received from the web sockets.
         */
        try {
            const betItem = (currentBet as typeof bet).bets
                .filter((item) => item.status === BetStatus.ACTIVE)
                .find((item) => {
                    const updatedActiveBet = bet.bets.find(
                        (updatedItem) => updatedItem.id === item.id,
                    );
                    return updatedActiveBet?.status !== BetStatus.ACTIVE;
                });

            return delays[betItem?.gameId] || 0;
        } catch (error) {
            return 0;
        }
    }

    return delays[bet.gameId] || 0;
};

export const getBatchDelay = (
    currentState: IGenericBet[],
    bets: IGenericBet[],
    delays: Record<string, number>,
): number =>
    bets.reduce((acc, bet) => {
        return Math.max(acc, getDelay(currentState, bet, delays));
    }, 0);
