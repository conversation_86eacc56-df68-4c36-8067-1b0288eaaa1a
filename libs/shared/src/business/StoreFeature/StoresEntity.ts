import { Store } from '@betgames/bg-state-manager';

// TODO: migrate to bg-state-manager
// create as a new class to avoid collisions and huge refactoring

export class StoresEntity<Parameter, StoreClass extends Store<unknown>> {
    private readonly stores = new Map<Parameter, StoreClass>();

    constructor(private readonly StoreClass: new (param: Parameter) => StoreClass) {}

    public store(param: Parameter): StoreClass {
        if (this.stores.has(param)) {
            return this.stores.get(param);
        }

        this.stores.set(param, new this.StoreClass(param));

        return this.stores.get(param);
    }
}
