import { RouletteOptionsClasses } from 'betgames-contract-js';
import { httpClientRepository } from '@bg-shared/infrastructure/HttpClient';
import { API_URLS } from '@bg-shared/constants/apiUrls';
import { DASH } from '@bg-shared/constants/textSymbols';
import { GameId } from '@bg-shared/enums/GameId';
import { replaceTemplate } from '@bg-shared/utils/replaceTemplate';
import { IRuleTranslations, ITranslations, ITranslationsResponse } from './interfaces';
import { IReplacements, validateKey } from './utils/validateKey';

export default class TranslationsStore {
    private translations: ITranslations;

    public update(translations: ITranslations): void {
        this.translations = translations;
    }

    public init(language: string, partnerCode: string): Promise<void> {
        return httpClientRepository
            .get<ITranslationsResponse>(`${API_URLS.TRANSLATIONS}/${language}/${partnerCode}`, {
                transformResponse: false,
            })
            .then((response) => {
                this.update(response.translations);
            });
    }

    public hasKey<G extends keyof ITranslations, <PERSON> extends keyof ITranslations[G]>(
        groupKey: G,
        key: K,
    ): boolean {
        return this.translations[groupKey]?.[key] !== undefined;
    }

    public string = validateKey((key: string, replacements?: IReplacements) => {
        const { strings } = this.translations;

        const translation = strings && strings[key] ? strings[key] : key;

        return replacements ? replaceTemplate(translation, replacements) : translation;
    });

    public colors = validateKey((key: string) => {
        const { colors } = this.translations;

        return colors && colors[key] ? colors[key] : key;
    });

    public lottoRegulations = validateKey((key: GameId) => {
        const { lottoRegulations } = this.translations;

        return lottoRegulations && lottoRegulations[key]
            ? lottoRegulations[key]
            : `regulation_${key}`;
    });

    public baccaratWinnerName = validateKey((key: string) => {
        const { baccaratWinners } = this.translations;

        return baccaratWinners && baccaratWinners[key] ? baccaratWinners[key] : key;
    });

    public warWinnerName = validateKey((key: string) => {
        const { warWinners } = this.translations;

        return warWinners && warWinners[key] ? warWinners[key] : key;
    });

    public footballGridlOptions = validateKey((key: string) => {
        const { footballGridOptions } = this.translations;

        return footballGridOptions && footballGridOptions[key] ? footballGridOptions[key] : key;
    });

    public diceDuelOptions = validateKey((key: string) => {
        const { diceDuelOptions } = this.translations;

        return diceDuelOptions && diceDuelOptions[key] ? diceDuelOptions[key] : key;
    });

    public headsupWinnerName = validateKey((key: string) => {
        const { headsupWinners } = this.translations;

        return headsupWinners && headsupWinners[key] ? headsupWinners[key] : key;
    });

    public resultsAbbr = validateKey((key: string) => {
        const { resultsAbbr } = this.translations;

        return resultsAbbr && resultsAbbr[key] ? resultsAbbr[key] : key[0].toUpperCase();
    });

    public status = validateKey((key: string) => {
        const { statuses } = this.translations;

        return statuses && statuses[key] ? statuses[key] : DASH;
    });

    public betStatus = validateKey<number>((key: number) => {
        const { betStatuses } = this.translations;

        return betStatuses && betStatuses[key] ? betStatuses[key] : DASH;
    });

    public notification = validateKey((key: string) => {
        const { notifications } = this.translations;

        return notifications && notifications[key] ? notifications[key] : key;
    });

    public videoQuality = validateKey((key: string) => {
        const { videoQualities } = this.translations;

        return videoQualities && videoQualities[key] ? videoQualities[key] : key;
    });

    public oddsFormat = validateKey((key: string) => {
        const { oddsFormats } = this.translations;

        return oddsFormats && oddsFormats[key] ? oddsFormats[key] : key;
    });

    public error = validateKey((key: string) => {
        const { errors } = this.translations;

        if (!errors) {
            return key;
        }

        return (
            errors[key] ||
            key
                .split(/(\[.*?])\s/g)
                .filter((fragment) => fragment.length)
                .map((fragment) => errors[fragment] || fragment)
                .join(' ')
        );
    });

    public gameName = validateKey((id: number) => {
        const { gameNames } = this.translations;

        return gameNames && gameNames[id] ? gameNames[id] : `Game_${id}`;
    });

    public oddName = validateKey((id: number) => {
        const { odds } = this.translations;

        return odds && odds[id] ? odds[id] : `betting_option_${id}`;
    });

    public oddNameShort = validateKey((id: number) => {
        const { shortOdds } = this.translations;

        return shortOdds && shortOdds[id] ? shortOdds[id] : `betting_option_${id}`;
    });

    public oddGroupName = validateKey((id: number) => {
        const { oddGroups } = this.translations;

        return oddGroups && oddGroups[id] ? oddGroups[id] : `betting_option_group_${id}`;
    });

    public rouletteOddName = validateKey((optionsClass: RouletteOptionsClasses) => {
        const { rouletteOdds } = this.translations;

        return rouletteOdds && rouletteOdds[optionsClass]
            ? rouletteOdds[optionsClass]
            : `roulette_option_${optionsClass}`;
    });

    public pokerCombinationName = validateKey((id: number) => {
        const { pokerCombinations } = this.translations;

        return pokerCombinations && pokerCombinations[id]
            ? pokerCombinations[id]
            : `betting_option_${id}`;
    });

    public headsupCombinationName = validateKey((id: number) => {
        const { headsupCombinations } = this.translations;

        return headsupCombinations && headsupCombinations[id]
            ? headsupCombinations[id]
            : `betting_option_${id}`;
    });

    public rules(key: string, gameId: GameId): IRuleTranslations | null {
        const { rules } = this.translations;

        return rules[key] && rules[key][gameId] ? rules[key][gameId] : null;
    }

    public round(key: string, gameId: GameId): string {
        const { rounds } = this.translations;

        return rounds[gameId] && rounds[gameId][key] ? rounds[gameId][key] : null;
    }

    public promotions = validateKey((key: string) => {
        const { promotions } = this.translations;

        return promotions && promotions[key] ? promotions[key] : key;
    });

    public dates = validateKey((key: string) => {
        const { dates } = this.translations;

        return dates && dates[key] ? dates[key] : key;
    });

    public gamification = validateKey((key: string, replacements?: IReplacements) => {
        const { gamification } = this.translations;
        const translation = gamification && gamification[key] ? gamification[key] : key;

        return replacements ? replaceTemplate(translation, replacements) : translation;
    });

    public chat = validateKey((key: string) => {
        const { chat } = this.translations;

        return chat && chat[key] ? chat[key] : key;
    });

    public wheelStatistics = validateKey((key: string) => {
        const { wheelStatistics } = this.translations;

        return wheelStatistics && wheelStatistics[key] ? wheelStatistics[key] : key;
    });

    public gamesCategories = validateKey((key: string) => {
        const { gamesCategories } = this.translations;

        return gamesCategories && gamesCategories[key] ? gamesCategories[key] : key;
    });
}
