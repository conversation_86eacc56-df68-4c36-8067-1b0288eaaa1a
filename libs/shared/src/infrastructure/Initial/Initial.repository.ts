import {
    HttpClientRepository,
    IRequestOptions,
    httpClientRepository,
} from '@bg-shared/infrastructure/HttpClient';
import { API_URLS } from '@bg-shared/constants/apiUrls';
import { IResponseInitial } from './interfaces';

export default class InitialRepository {
    constructor(private readonly httpClient: HttpClientRepository) {}

    fetch(partnerCode: string, requestObject?: IRequestOptions): Promise<IResponseInitial> {
        const timestampRequestStart = Date.systemNow();

        return this.httpClient
            .get<IResponseInitial>(`${API_URLS.INITIAL}/${partnerCode}`, requestObject)
            .then((response) => {
                // compare system time and server time
                const timeDifference =
                    Date.systemNow() -
                    Math.round(response.now + (Date.systemNow() - timestampRequestStart) / 2);

                // always sync client time with server time
                // because affects timer calculations where rounding to seconds is applied
                Object.defineProperty(window.Date, 'now', {
                    get: () => () => {
                        return Date.systemNow() - timeDifference;
                    },
                    enumerable: false,
                });

                return response;
            });
    }
}

export const initialRepository = new InitialRepository(httpClientRepository);
