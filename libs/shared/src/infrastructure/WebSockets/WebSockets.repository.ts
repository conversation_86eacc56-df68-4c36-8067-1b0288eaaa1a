import { io, Socket } from 'socket.io-client';
import { sum } from 'lodash-unified';
import {
    camelCaseKeys,
    visibility,
    setImmediateTimeout,
    requestInterval,
} from '@betgames/bg-tools';
import { refreshIframe } from '@bg-shared/utils/refreshIframe';
import { REFRESH_CONNECTION, LIST_TYPE_DATA_CHANNELS } from '@bg-shared/constants/socketChannels';
import { IConfig, IError } from './interfaces';

enum ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    RECONNECTING,
}

interface IConnectionMetrics {
    disconnects: number;
    reconnects: number;
    lastDisconnectReason: string;
    lastConnectedAt: number;
    lastDisconnectedAt: number;
    averageReconnectTime: number;
    reconnectDurations: number[];
    errors?: Record<string, number>;
}

export default class WebSocketsRepository {
    private readonly ACKNOWLEDGEMENT_TIMEOUT = 5000;

    private readonly REFRESH_CONNECTION_INTERVAL = 300000; // 5 minutes

    private readonly HEARTBEAT_INTERVAL = 30000; // 30 seconds

    private readonly HEARTBEAT_TIMEOUT = 10000; // 10 seconds

    private readonly MAX_RECONNECT_ATTEMPTS = 10;

    private readonly BASE_RECONNECT_DELAY = 1000; // 1 second

    private readonly MAX_RECONNECT_DELAY = 30000; // 30 seconds

    private readonly RECONNECT_FACTOR = 2;

    private readonly INACTIVITY_THRESHOLD = 60000; // 1 minute

    private readonly DELAY_BEFORE_REFRESH = 3000; // 3 seconds

    private readonly subscriptions = new Set<string>();

    private visibilityPausedAt: number | null = null;

    private onReconnectCallbacks = new Set<() => void>();

    private onConnectionStatusCallbacks = new Set<(status: string) => void>();

    private instance: Socket;

    private onConnectCounter = 0;

    private connectionState: ConnectionState = ConnectionState.DISCONNECTED;

    private connectionAttempts = 0;

    private heartbeatTimer: number | null = null;

    private heartbeatTimeoutTimer: number | null = null;

    private refreshTokenInterval: (() => void) | null = null;

    private connectionConfig: IConfig | null = null;

    private connectionMetrics: IConnectionMetrics = {
        disconnects: 0,
        reconnects: 0,
        lastDisconnectReason: '',
        lastConnectedAt: 0,
        lastDisconnectedAt: 0,
        averageReconnectTime: 0,
        reconnectDurations: [],
        errors: {},
    };

    private client = (): Socket => {
        if (!this.instance) {
            throw Error(`You probably forgot to configure webSockets,
        please use "configure(url: string, token: string, partnerCode: string): void; method,
        before using webSockets API"`);
        }

        return this.instance;
    };

    private logError(message: string): void {
        console.error(`WS error: ${message}`, this.connectionMetrics);
    }

    private categorizeError(error: string): string {
        if (error.includes('auth') || error.includes('token')) {
            return 'authentication';
        }

        if (error.includes('rate limit')) {
            return 'rate_limit';
        }

        if (error.includes('timeout')) {
            return 'timeout';
        }

        return 'general';
    }

    private isTransientError(error: string): boolean {
        return (
            error.includes('timeout') ||
            error.includes('network') ||
            error.includes('transport') ||
            error.includes('connection')
        );
    }

    private getConnectionState(): string {
        switch (this.connectionState) {
            case ConnectionState.CONNECTED:
                return 'connected';
            case ConnectionState.CONNECTING:
                return 'connecting';
            case ConnectionState.RECONNECTING:
                return 'reconnecting';
            case ConnectionState.DISCONNECTED:
                return 'disconnected';
            default:
                return 'unknown';
        }
    }

    private forceReconnect(): void {
        if (this.instance) {
            console.warn('Forcing reconnection...');
            this.client().disconnect();
            this.client().connect();
        }
    }

    private resubscribe(): void {
        if (this.subscriptions.size) {
            const list = Array.from(this.subscriptions.values());

            this.client().emit('subscribe', list);

            list.forEach((name) => {
                this.emitLegacyLast(name);
            });
        }
    }

    private calculateReconnectDelay(): number {
        const delay = Math.min(
            this.BASE_RECONNECT_DELAY * this.RECONNECT_FACTOR ** this.connectionAttempts,
            this.MAX_RECONNECT_DELAY,
        );
        // Add jitter to prevent thundering herd problem
        // https://en.wikipedia.org/wiki/Thundering_herd_problem
        const jitter = Math.random() * 0.3 * delay;
        return delay + jitter;
    }

    private checkConnectionWithPing(): void {
        const pingTimeout = setTimeout(() => {
            this.logError('No ping response - connection might be dead');
            this.forceReconnect();
        }, this.ACKNOWLEDGEMENT_TIMEOUT);

        this.client().emit('ping', () => {
            clearTimeout(pingTimeout);
        });
    }

    private setupHeartbeat(): void {
        const clearHeartbeatTimers = () => {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;

            clearTimeout(this.heartbeatTimeoutTimer);
            this.heartbeatTimeoutTimer = null;
        };

        const sendHeartbeat = () => {
            clearTimeout(this.heartbeatTimeoutTimer);

            this.client().emit('heartbeat');

            this.heartbeatTimeoutTimer = setTimeout(() => {
                // Connection is dead but socket.io doesn't know it
                this.logError('Heartbeat timeout - forcing reconnection');
                this.client().disconnect();
                this.client().connect();
            }, this.HEARTBEAT_TIMEOUT);
        };

        this.client().on('connect', () => {
            clearHeartbeatTimers();
            this.heartbeatTimer = setInterval(sendHeartbeat, this.HEARTBEAT_INTERVAL);
        });

        this.client().on('pong', () => {
            clearTimeout(this.heartbeatTimeoutTimer);
            this.heartbeatTimeoutTimer = null;
        });

        this.client().on('disconnect', () => {
            clearHeartbeatTimers();
        });
    }

    private setupVisibilityHandling(): void {
        let timeoutId = 0;

        visibility.setEvent((isHidden: boolean) => {
            clearTimeout(timeoutId);

            if (isHidden) {
                // Window is hidden, pause heartbeats to save resources
                this.visibilityPausedAt = Date.now();

                if (this.heartbeatTimer) {
                    clearInterval(this.heartbeatTimer);
                    this.heartbeatTimer = null;
                }
            } else {
                // Window is visible again
                const now = Date.now();
                const inactivityDuration = this.visibilityPausedAt
                    ? now - this.visibilityPausedAt
                    : 0;
                this.visibilityPausedAt = null;

                // setTimeout is added here to jump to end of callstack
                timeoutId = setImmediateTimeout(() => {
                    if (this.connectionState === ConnectionState.DISCONNECTED) {
                        // If disconnected, try reconnecting first before refreshing iframe
                        if (inactivityDuration > this.INACTIVITY_THRESHOLD) {
                            console.warn(
                                `Reconnecting after ${inactivityDuration}ms of inactivity`,
                            );
                            this.forceReconnect();

                            // If still disconnected after a short delay, refresh the iframe
                            setTimeout(() => {
                                if (this.connectionState === ConnectionState.DISCONNECTED) {
                                    refreshIframe();
                                }
                            }, this.DELAY_BEFORE_REFRESH);
                        } else {
                            refreshIframe();
                        }
                    } else if (this.connectionState === ConnectionState.CONNECTED) {
                        // If connected but inactive for a while, check connection with a ping
                        if (inactivityDuration > this.INACTIVITY_THRESHOLD) {
                            this.checkConnectionWithPing();
                        }

                        // Resume heartbeats
                        if (!this.heartbeatTimer) {
                            this.setupHeartbeat();
                        }
                    }
                });
            }
        });
    }

    private updateConnectionStatus(status: ConnectionState, reason?: string): void {
        this.connectionState = status;

        const statusString = this.getConnectionState();

        this.onConnectionStatusCallbacks.forEach((callback) => {
            callback(statusString);
        });

        // Dispatch a custom event for application-wide notification
        // TODO: add some overlay with message for user
        const event = new CustomEvent('websocket-status', {
            detail: { status: statusString, reason, timestamp: Date.now() },
        });
        window.dispatchEvent(event);
    }

    private runRefreshToken(): void {
        if (this.refreshTokenInterval) {
            this.refreshTokenInterval();
        }

        if (!this.connectionConfig) {
            this.logError('Cannot refresh token: connection not configured');
            return;
        }

        this.refreshTokenInterval = requestInterval(() => {
            this.client().emit(REFRESH_CONNECTION, this.connectionConfig?.token);
        }, this.REFRESH_CONNECTION_INTERVAL);
    }

    public configure(config: IConfig, onReconnect?: () => void): void {
        this.connectionConfig = config;
        this.connectionState = ConnectionState.CONNECTING;

        this.instance = io(config.url, {
            transports: ['websocket'],
            query: {
                token: config.token,
                platform: 'iframe-webapp',
                playerId: config.playerId,
                partnerCode: config.partnerCode,
                cashOutOnDisconnectThreshold: JSON.stringify(config.cashOutOnDisconnectThreshold),
            },
            reconnectionAttempts: this.MAX_RECONNECT_ATTEMPTS,
            reconnectionDelay: this.calculateReconnectDelay(),
            reconnectionDelayMax: this.MAX_RECONNECT_DELAY,
            randomizationFactor: 0.3,
        });

        if (onReconnect) {
            this.onReconnectCallbacks.add(onReconnect);
        }

        const emitAuthentication = (): void => {
            this.client().emit('authentication', {
                auth_key: config.token,
            });
        };

        window.addEventListener(
            'beforeunload',
            () => {
                this.client().disconnect();
            },
            false,
        );

        this.setupVisibilityHandling();
        this.setupHeartbeat();

        this.instance
            .on('connect', () => {
                const now = Date.now();
                this.onConnectCounter++;
                this.connectionAttempts = 0;
                this.updateConnectionStatus(ConnectionState.CONNECTED);

                // Update metrics
                if (this.onConnectCounter > 1) {
                    this.connectionMetrics.reconnects++;
                    const reconnectDuration = now - this.connectionMetrics.lastDisconnectedAt;
                    this.connectionMetrics.reconnectDurations.push(reconnectDuration);

                    // Calculate average reconnect time
                    this.connectionMetrics.averageReconnectTime =
                        sum(this.connectionMetrics.reconnectDurations) /
                        this.connectionMetrics.reconnectDurations.length;
                }

                this.connectionMetrics.lastConnectedAt = now;

                this.resubscribe();
                this.runRefreshToken();

                if (this.onConnectCounter > 1) {
                    this.onReconnectCallbacks.forEach((callback) => {
                        callback();
                    });
                }
            })
            .on('reconnect', () => {
                this.updateConnectionStatus(ConnectionState.CONNECTED);
                emitAuthentication();
                this.runRefreshToken();
                this.resubscribe();

                this.onReconnectCallbacks.forEach((callback) => {
                    callback();
                });
            })
            .on('reconnect_attempt', (attempt) => {
                this.connectionAttempts = attempt;
                this.updateConnectionStatus(ConnectionState.RECONNECTING);
                console.warn(`Reconnection attempt ${attempt}/${this.MAX_RECONNECT_ATTEMPTS}`);
            })
            .on('reconnect_failed', () => {
                this.logError('Failed to reconnect after maximum attempts');
                this.updateConnectionStatus(ConnectionState.DISCONNECTED, 'max_attempts_reached');
                refreshIframe();
            })
            .on('error', (error: string) => {
                // Track error metrics
                if (!this.connectionMetrics.errors) {
                    this.connectionMetrics.errors = {};
                }

                const errorType = this.categorizeError(error);
                this.connectionMetrics.errors[errorType] =
                    (this.connectionMetrics.errors[errorType] || 0) + 1;

                this.logError(`[TYPE]=${errorType} - ${error}`);

                // Dispatch error event for application-wide notification
                const event = new CustomEvent('websocket-error', {
                    detail: {
                        error,
                        timestamp: Date.now(),
                        connectionState: this.getConnectionState(),
                    },
                });
                window.dispatchEvent(event);

                // Handle specific error types
                if (error.includes('auth') || error.includes('token')) {
                    this.updateConnectionStatus(
                        ConnectionState.DISCONNECTED,
                        'authentication_failed',
                    );
                    emitAuthentication();
                    this.runRefreshToken();
                } else if (this.isTransientError(error)) {
                    // For transient errors, attempt reconnection with backoff
                    setTimeout(() => {
                        this.forceReconnect();
                    }, this.calculateReconnectDelay());
                } else {
                    setTimeout(() => {
                        refreshIframe();
                    }, this.DELAY_BEFORE_REFRESH);
                }
            })
            .on('disconnect', (reason: string) => {
                this.connectionMetrics.disconnects++;
                this.connectionMetrics.lastDisconnectReason = reason;
                this.connectionMetrics.lastDisconnectedAt = Date.now();

                // socket.io doesn't reconnect automatically when the connection had been dropped by the server
                // so let's reconnect manually in such cases
                if (reason === 'io server disconnect') {
                    this.client().connect();
                }

                this.updateConnectionStatus(ConnectionState.DISCONNECTED, reason);
            });
    }

    public onReconnect(callback: () => void): () => void {
        this.onReconnectCallbacks.add(callback);

        return () => {
            this.onReconnectCallbacks.delete(callback);
        };
    }

    public on<T = unknown>(
        event: string | string[],
        listener: (response: T, acknowledgment?: () => void) => void,
        subscribe = true,
    ): () => void {
        const events = Array.isArray(event) ? event : [event];

        const listenerWrapper = (data: unknown, acknowledgment?: () => void): void => {
            let parsedData = data as T;

            if (typeof parsedData === 'string') {
                try {
                    parsedData = JSON.parse(data as string);
                } catch {}
            }

            try {
                parsedData = camelCaseKeys(parsedData) as T;
            } catch {}

            listener(parsedData, acknowledgment);
        };

        events.forEach((name) => {
            this.client().on(name, listenerWrapper);
        });

        const subscribed = events.every((name) => this.subscriptions.has(name));

        if (subscribe && !subscribed) {
            this.client().emit('subscribe', events);
            events.forEach((name) => this.subscriptions.add(name));
        }

        return () => {
            events.forEach((name) => {
                this.client().off(name, listenerWrapper);
            });

            const hasListeners = events.every((name) => this.client().hasListeners(name));

            if (subscribe && !hasListeners) {
                events.forEach((name) => {
                    this.subscriptions.delete(name);
                    this.client().emit('unsubscribe', name);
                });
            }
        };
    }

    public emitLast<R extends unknown | IError>(
        channel: string,
        callback?: (data: R) => void,
    ): void {
        this.client().emit(
            LIST_TYPE_DATA_CHANNELS.some((item) => channel.includes(item))
                ? 'cache:list'
                : 'cache:last',
            channel,
            (data: R) => {
                if (callback && !(data as IError).error) {
                    let parsedData = data as R;

                    try {
                        parsedData = camelCaseKeys(parsedData) as R;
                    } catch {}

                    callback(parsedData);
                }
            },
        );
    }

    public emitLegacyLast(channel: string): void {
        this.client().emit('last', channel);
    }

    public getCache<R = unknown>(event: string): Promise<R> {
        return new Promise((resolve, reject) => {
            this.client()
                .timeout(this.ACKNOWLEDGEMENT_TIMEOUT)
                .emit(
                    LIST_TYPE_DATA_CHANNELS.some((item) => event.includes(item))
                        ? 'cache:list'
                        : 'cache:last',
                    event,
                    (err: unknown, data: R) => {
                        if (err) {
                            reject(new Error('Network timeout'));
                        } else {
                            let parsedData = data as R;

                            try {
                                parsedData = camelCaseKeys(parsedData) as R;
                            } catch {}

                            const dataWithError = data as { error: IError };

                            if (dataWithError.error) {
                                reject(new Error(dataWithError.error.message));
                            } else {
                                resolve(parsedData);
                            }
                        }
                    },
                );
        });
    }

    public request<R = unknown>(event: string, params: unknown = null): Promise<R> {
        return new Promise((resolve, reject) => {
            if (this.connectionState !== ConnectionState.CONNECTED) {
                reject(new Error('Socket not connected'));
                return;
            }

            this.client()
                .timeout(this.ACKNOWLEDGEMENT_TIMEOUT)
                .emit(event, params, (err: unknown, data: R) => {
                    if (err) {
                        reject(new Error('Network timeout'));
                    } else {
                        let parsedData = data as R;

                        try {
                            parsedData = camelCaseKeys(parsedData) as R;
                        } catch {}

                        const dataWithError = data as { error: IError };

                        if (dataWithError.error) {
                            reject(new Error(dataWithError.error.message));
                        } else {
                            resolve(parsedData);
                        }
                    }
                });
        });
    }

    public emit<R extends unknown | IError>(
        event: string,
        data: unknown,
        callback?: (data: R) => void,
    ): void {
        this.client().emit(event, data, (eventData: R) => {
            if (callback) {
                let parsedData = eventData as R;

                try {
                    parsedData = camelCaseKeys(parsedData) as R;
                } catch {}

                callback(parsedData);
            }
        });
    }
}
