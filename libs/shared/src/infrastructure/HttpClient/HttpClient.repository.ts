import 'whatwg-fetch';
import { stringify } from 'qs';
import { snakeCaseKeys, getValidUrl, camelCaseKeys } from '@betgames/bg-tools';
import { envConfig } from '@bg-shared/constants/envConfig';
import { IRequestOptions } from './interfaces';

const HTTP_RESPONSE_NO_CONTENT_CODE = 204;

export default class HttpClientRepository {
    private baseApiUrl = envConfig.apiUrl;

    private headers: Record<string, string> = {
        'Content-Type': 'application/json;charset=UTF-8',
        Accept: 'application/json',
        Client: 'iframe',
        'Client-Version': envConfig.version,
    };

    private headersHandler: (response: Response) => void;

    public setHeadersHandler(handler: typeof this.headersHandler): void {
        this.headersHandler = handler;
    }

    private formatUrl = <P = unknown>(
        url: string,
        params: P,
        transformRequest = true,
        sameHost = false,
    ): string => {
        let requestURL = sameHost ? url : `${this.baseApiUrl}${url}`;

        if (params) {
            requestURL += `?${stringify(transformRequest ? snakeCaseKeys(params) : params)}`;
        }

        return requestURL;
    };

    private isNetworkError(error: Error | DOMException | TypeError): boolean {
        // Check for TypeError (most common network error type)
        if (error instanceof TypeError) {
            // Exclude JSON parsing errors
            return !error.message.toLowerCase().includes('json');
        }

        // Check for AbortError
        if (error.name === 'AbortError') {
            return true;
        }

        // Check for DOMException with network-related names
        if (
            error instanceof DOMException &&
            ['NetworkError', 'TimeoutError', 'AbortError'].includes(error.name)
        ) {
            return true;
        }

        // Check for specific error codes (Node.js environments)
        if (
            (error as Error & { code?: string }).code &&
            ['ENOTFOUND', 'ECONNREFUSED', 'ETIMEDOUT', 'ECONNRESET'].includes(
                (error as Error & { code?: string }).code,
            )
        ) {
            return true;
        }

        // Additional check for offline status
        if (!navigator.onLine) {
            return true;
        }

        // Check if error has no status property (likely network failure)
        return typeof error === 'object' && !('status' in error) && 'message' in error;
    }

    private makeRequest<T, P>(url: string, requestOptions: IRequestOptions<P>): Promise<T> {
        const maxRetries = requestOptions.maxRetries || 0;
        const requestURL = this.formatUrl<P>(
            url,
            requestOptions.params,
            requestOptions.transformRequest,
            requestOptions.sameHost,
        );
        const transformResponse =
            typeof requestOptions.transformResponse === 'boolean'
                ? requestOptions.transformResponse
                : true;
        const transformedData = requestOptions.transformRequest
            ? snakeCaseKeys(requestOptions.data || {})
            : requestOptions.data;

        const doRequest = async (): Promise<T> => {
            const response = (await Promise.race([
                fetch(requestURL, {
                    ...requestOptions.options,
                    method: requestOptions.options?.method || 'GET',
                    headers: {
                        ...this.headers,
                        ...requestOptions.options?.headers,
                    },
                    body:
                        requestOptions.options?.method === 'GET'
                            ? undefined
                            : JSON.stringify(transformedData),
                }),
                ...(requestOptions.timeout
                    ? [
                          new Promise((_, reject) => {
                              setTimeout(() => {
                                  reject(new Error('Request timed out'));
                              }, requestOptions.timeout);
                          }),
                      ]
                    : []),
            ])) as Response;

            this.headersHandler?.(response);

            if (response.status === HTTP_RESPONSE_NO_CONTENT_CODE) {
                return null as T;
            }

            const responseData = await response.json();

            if (typeof responseData !== 'object' || responseData === null) {
                return responseData as T;
            }

            if ('error' in responseData || response.status >= 400) {
                throw {
                    status: response.status,
                    ...responseData,
                };
            }

            return transformResponse ? (camelCaseKeys(responseData) as T) : (responseData as T);
        };

        const retry = (retryCount: number): Promise<T> => {
            return doRequest().catch(async (error) => {
                if (retryCount < maxRetries) {
                    console.error(`Request failed (Retry ${retryCount + 1}):`, error);

                    // Delay before retrying (you can adjust the delay as needed)
                    await new Promise<void>((resolve) => {
                        setTimeout(resolve, 1000);
                    });

                    return retry(retryCount + 1);
                }

                if (this.isNetworkError(error)) {
                    throw new Error('network_error');
                }

                throw error;
            });
        };

        return retry(0);
    }

    public get apiUrl(): string {
        return this.baseApiUrl;
    }

    public set apiUrl(apiUrl: string) {
        this.baseApiUrl = apiUrl;
    }

    public configureApiUrl(url: string): void {
        const apiUrl = getValidUrl(url);

        if (apiUrl) {
            this.baseApiUrl = apiUrl;
        }
    }

    public get<T = unknown, P = unknown>(
        url: string,
        requestOptions: IRequestOptions<P> = {},
    ): Promise<T> {
        return this.makeRequest<T, P>(url, {
            ...requestOptions,
            options: {
                ...requestOptions.options,
                method: 'GET',
            },
        });
    }

    public post<T = unknown, P = unknown>(
        url: string,
        requestObject: IRequestOptions<P> = {
            transformRequest: true,
        },
    ): Promise<T> {
        const transformed =
            requestObject.data && requestObject.transformRequest
                ? snakeCaseKeys(requestObject.data)
                : requestObject.data;

        return this.makeRequest<T, P>(url, {
            ...requestObject,
            options: {
                ...requestObject.options,
                method: 'POST',
                body: JSON.stringify(transformed),
            },
        });
    }

    public remove<T = unknown, P = Record<string, unknown>>(
        url: string,
        requestObject: IRequestOptions<P> = {},
    ): Promise<T> {
        return this.makeRequest<T, P>(url, {
            ...requestObject,
            options: {
                ...requestObject.options,
                method: 'DELETE',
            },
        });
    }

    public setHeaders(newHeaders: Record<string, string>): void {
        this.headers = {
            ...this.headers,
            ...newHeaders,
        };
    }

    public removeHeader(name: string): void {
        delete this.headers[name];
    }
}

export const httpClientRepository = new HttpClientRepository();
