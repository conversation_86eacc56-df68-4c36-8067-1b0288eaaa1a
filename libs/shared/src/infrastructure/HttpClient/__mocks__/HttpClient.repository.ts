import { getValidUrl } from '@betgames/bg-tools';

export default class HttpClientRepository {
    private apiUrlString: string;

    public get apiUrl(): string {
        return this.apiUrlString;
    }

    public get() {
        return Promise.resolve();
    }

    public post() {
        return Promise.resolve();
    }

    public configureApiUrl(url: string): void {
        const apiUrl = getValidUrl(url);

        if (apiUrl) {
            this.apiUrlString = apiUrl;
        } else {
            throw new Error('apiUrl is not valid, please provide valid url structure');
        }
    }
}

export const httpClientRepository = new HttpClientRepository();
