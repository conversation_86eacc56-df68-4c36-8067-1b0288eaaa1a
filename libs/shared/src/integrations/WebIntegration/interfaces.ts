import { ModalsManager } from '@bg-shared/contexts/ModalsManager/ModalsManager';
import { GameId } from '@bg-shared/enums/GameId';

export interface IBasicWebIntegration {
    balanceUpdate(amount: string): void;

    gameLoadStarted(): void;

    gameLoadFinished(): void;

    updateSound(muted: boolean): void;

    exitGame(): void;

    navigate(gameID?: GameId): void;

    runFinished(params?: { balance: string; prevRunId: number | string }): void;

    errorMessage(message: string, code?: number): void;

    registerModalsManager(manager: ModalsManager): void;
}

export interface IBGIWebIntegration extends IBasicWebIntegration {
    runStarted(): void;

    stakeUpdate(amount: number): void;

    jackpotWinUpdate(amount: number): void;
}

export interface IBGIIntegrationEvent {
    playerId: number;
    message: {
        code: string;
        message: string;
    };
}

declare global {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    interface Window {
        // BGI API
        com: {
            ppb: {
                constants: {
                    GO_TO_GAME: string;
                    EVENT_EXIT_GAME: string;
                    BALANCE_CHANGED: string;
                    SETTINGS_UPDATE: string;
                    NOTIFY_ACTION: string;
                };

                gc: {
                    sendGameLoadStarted(): void;

                    sendGameLoadCompleted(): void;

                    sendGameAnimationStart(): void;

                    sendGameAnimationCompleted(): void;

                    requestGameWindowConfig(): void;

                    sendBalanceUpdate(
                        params: { realAmount: number; bonusAmount?: number },
                        currency: string,
                    ): void;

                    sendStakeUpdate(amount: number, currency: string): void;

                    sendWinUpdate(amount: number, currency: string): void;

                    sendJackpotWinUpdate(amount: number, currency: string): void;

                    sendToggleMenu(): void;

                    sendSettingsUpdate(setting: 'sound', value: 'on' | 'off'): void;

                    sendErrorMessage(
                        code: string,
                        message: string,
                        params?: {
                            gamingSessionDuration: number;
                            plAmount: number;
                            currencyCode: string;
                        },
                    ): void;

                    sendEvent(event: string, params?: Record<string, unknown>): void;

                    registerCallbackFunction<T = unknown>(
                        event: string,
                        callback: (payload?: T) => void,
                    ): void;
                };
            };
        };
    }
}
