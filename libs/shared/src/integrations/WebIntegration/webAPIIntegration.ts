import { envConfig } from '@bg-shared/constants/envConfig';
import { WebIntegrationType } from './enums';
import { IBasicWebIntegration } from './interfaces';

const instances = new Map<WebIntegrationType, IBasicWebIntegration>();

const createLoader = (type: WebIntegrationType) =>
    import(`./integrations/${type.toUpperCase()}WebIntegration`).then((module) => {
        if (instances.has(type)) {
            return instances.get(type);
        }
        // eslint-disable-next-line new-cap
        const instance = new module.default();

        instances.set(type, instance);

        return instance;
    });

const integrationsLoaders: Record<WebIntegrationType, () => Promise<IBasicWebIntegration>> = {
    [WebIntegrationType.FEIM]: () => createLoader(WebIntegrationType.FEIM),
    [WebIntegrationType.BGI]: () => createLoader(WebIntegrationType.BGI),
};

const loadIntegration = async <T extends IBasicWebIntegration>(
    type: WebIntegrationType,
): Promise<T | null> => {
    const loader = integrationsLoaders[type];
    return loader ? ((await loader()) as T) : null;
};

export const webAPIIntegration = <T extends IBasicWebIntegration>(
    type: WebIntegrationType | null,
    action: (api: T) => void,
): void => {
    loadIntegration<T>(type)
        .then((integration) => {
            if (integration) {
                action(integration);
            }
        })
        .catch((error) => {
            if (envConfig.isTestingEnv) {
                console.error('Failed to execute web integration action', error);
            }
        });
};
