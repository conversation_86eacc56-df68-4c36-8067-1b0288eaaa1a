import './libs/ppb-game-client-api-1.0.9.js';
import currency from 'currency.js';
import { debounce } from 'lodash-unified';
import {
    balanceService,
    soundsStore,
    partnerSettingsStore,
    recentBetsSelectors,
    recentBetsStore,
} from '@bg-shared/business';
import { GameId, ModalName } from '@bg-shared/enums';
import { ModalsManager } from '@bg-shared/contexts/ModalsManager';
import { webSocketsRepository } from '@bg-shared/infrastructure';
import { HowToPlayModal } from '@bg-components/Modals/HowToPlayModal';
import { IBGIIntegrationEvent, IBGIWebIntegration } from '../interfaces';
import { WebIntegrationErrorCodes } from '../enums';

export default class BGIWebIntegration implements IBGIWebIntegration {
    private readonly controller = window.com.ppb.gc;

    private readonly constants = window.com.ppb.constants;

    private modalsManager: ModalsManager;

    private gameRestarted = false;

    private isLastWonReset = false;

    private ERROR_MESSAGE_DEBOUNCE = 500;

    private ERROR_MESSAGE_WAIT = 3000;

    private errorMessageQueue: { code: number | string; message: string }[] = [];

    private lastShownError: { code: number | string; message: string; timestamp?: number };

    private ignoredMessages = ['INVALID_INPUT_ERROR', 'transaction_failed_bet_timeout'];

    private debouncedErrorMessage = debounce(
        () => {
            // for the cases that API response overlaps with ws events OR
            // multiple same messages are received via ws events.

            this.errorMessageQueue = this.errorMessageQueue.filter(
                (item) =>
                    // filter out ignored messages. should be displayed only inside our iframe.
                    !this.ignoredMessages.includes(item.message) &&
                    // an error that doesn't have valid message
                    !Number(item.message),
            );

            const lastValidError =
                this.errorMessageQueue.find(
                    (error) =>
                        error.code && error.code !== WebIntegrationErrorCodes.GENERAL_UI_BOUNDARY,
                ) || this.errorMessageQueue.pop();

            if (!lastValidError) {
                return;
            }

            if (
                // handling delayed ws events
                lastValidError.code !== this.lastShownError?.code ||
                Date.now() - (this.lastShownError?.timestamp || 0) > this.ERROR_MESSAGE_WAIT
            ) {
                const errorCode =
                    lastValidError.code || WebIntegrationErrorCodes.GENERAL_UI_BOUNDARY;

                this.controller.sendErrorMessage(errorCode.toString(), lastValidError.message);

                this.lastShownError = {
                    code: errorCode,
                    message: lastValidError.message,
                    timestamp: Date.now(),
                };
            }

            this.errorMessageQueue = [];
        },
        this.ERROR_MESSAGE_DEBOUNCE,
        { leading: true },
    );

    constructor() {
        this.controller.registerCallbackFunction<{
            settingName: 'sound';
            settingValue: boolean;
        }>(this.constants.SETTINGS_UPDATE, (payload) => {
            if (payload.settingName === 'sound') {
                soundsStore.setMasterMuted(!payload.settingValue);
            }
        });

        this.controller.registerCallbackFunction(this.constants.BALANCE_CHANGED, () => {
            this.sendBalance();
        });

        this.controller.registerCallbackFunction<{ actionName: string }>(
            this.constants.NOTIFY_ACTION,
            (payload) => {
                switch (payload.actionName) {
                    case 'gameHelp':
                        this.modalsManager.open(ModalName.HOW_TO_PLAY, HowToPlayModal);
                        break;
                    case 'exitingGame':
                        this.gameRestarted = true;
                        break;
                    default:
                        break;
                }
            },
        );

        webSocketsRepository.on<IBGIIntegrationEvent>('integration', (data) => {
            this.errorMessage(data.message.message, data.message.code);
        });
    }

    private sendBalance(): void {
        balanceService.ping().then((value) => {
            if (value !== null) {
                this.balanceUpdate(value);
            }
        });
    }

    private parseAmount(value: string | number) {
        return currency(value).value;
    }

    public registerModalsManager(manager: ModalsManager): void {
        this.modalsManager = manager;
    }

    public gameLoadStarted(): void {
        this.controller.sendGameLoadStarted();
    }

    public gameLoadFinished(): void {
        this.controller.sendGameLoadCompleted();
        this.sendBalance();
    }

    public balanceUpdate(value: string): void {
        this.controller.sendBalanceUpdate(
            {
                realAmount: this.parseAmount(value),
            },
            partnerSettingsStore.currency.code,
        );
    }

    public updateSound(muted: boolean): void {
        this.controller.sendSettingsUpdate('sound', muted ? 'off' : 'on');
    }

    public runStarted(): void {
        this.controller.sendGameAnimationStart();
    }

    public runFinished(params: { prevRunId: number | string }): void {
        setTimeout(() => {
            const winAmount = recentBetsSelectors.getRunWinAmount(params.prevRunId)(
                recentBetsStore.value,
            );

            this.isLastWonReset = false;

            this.controller.sendWinUpdate(winAmount, partnerSettingsStore.currency.code);

            // We have to wait until the recent bets are updated and then emit win amount to the partner
            // 400ms was confirmed from the partner side as a viable number.
        }, 400);
        this.controller.sendGameAnimationCompleted();
    }

    public exitGame(): void {
        if (!this.gameRestarted) {
            this.controller.sendEvent(this.constants.EVENT_EXIT_GAME);
        }

        this.gameRestarted = false;
    }

    public navigate(gameID: GameId): void {
        this.controller.sendEvent(this.constants.GO_TO_GAME, { gameCode: gameID });
    }

    public stakeUpdate(amount: number): void {
        if (!this.isLastWonReset) {
            this.controller.sendWinUpdate(0, partnerSettingsStore.currency.code);
            this.isLastWonReset = true;
        }

        this.controller.sendStakeUpdate(amount, partnerSettingsStore.currency.code);
    }

    public jackpotWinUpdate(amount: number): void {
        this.controller.sendJackpotWinUpdate(amount, partnerSettingsStore.currency.code);
    }

    public errorMessage(message: string, code: number | string): void {
        this.errorMessageQueue.push({ code, message });
        this.debouncedErrorMessage();
    }
}
