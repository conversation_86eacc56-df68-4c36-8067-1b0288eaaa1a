import { configure, on, send, IGameSettings } from '@rlx/feim';
import { sessionStore } from '@bg-shared/business/Session';
import { ModalsManager } from '@bg-shared/contexts/ModalsManager';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { soundsStore } from '@bg-shared/business/Sounds';
import { recentBetsStore } from '@bg-shared/business/RecentBets/RecentBets.store';
import { recentBetsSelectors } from '@bg-shared/business/RecentBets/recentBets.selectors';
import { feimSettingsStore } from '@bg-shared/business/FEIMSettings';
import { balanceService } from '@bg-shared/business/Balance';
import { ModalName } from '@bg-shared/enums/ModalName';
import { formatToCents } from '@bg-shared/utils/formatToCents';
import { HowToPlayModal } from '@bg-components/Modals/HowToPlayModal';
import { IBasicWebIntegration } from '../interfaces';
import { WebIntegrationErrorCodes } from '../enums';

export default class FEIMWebIntegration implements IBasicWebIntegration {
    private modalsManager: ModalsManager;

    constructor() {
        configure({
            handleRgPostMessageAPI: true,
            handleFeaturePause: true,
            p2pConfig: {
                currency:
                    sessionStore.session.currency.code ||
                    partnerSettingsStore.partnerSettings.currency.code,
            },
        });

        send.updateSettings({ sounds: false });

        on.freeze(() => {
            feimSettingsStore.freeze = true;
        });

        on.unfreeze(() => {
            feimSettingsStore.freeze = false;
        });

        on.updateSettings((newSetting: IGameSettings) => {
            if (typeof newSetting.sounds !== 'undefined') {
                soundsStore.setMasterMuted(!newSetting.sounds);
            }
        });

        // If show or hide, this event should do nothing if the game help is already visible or hidden,
        // respectively. If this (or the parent object) is undefined,
        // the action should be considered to be a toggle.
        on.toggleGameHelp((action: { type: 'show' | 'hide' | 'toggle' }) => {
            switch (action?.type) {
                case 'toggle':
                    this.modalsManager.toggle(ModalName.HOW_TO_PLAY, HowToPlayModal);
                    break;
                case 'show':
                    this.modalsManager.open(ModalName.HOW_TO_PLAY, HowToPlayModal);
                    break;
                case 'hide':
                    this.modalsManager.close(ModalName.HOW_TO_PLAY);
                    break;
                default:
                    this.modalsManager.toggle(ModalName.HOW_TO_PLAY, HowToPlayModal);
                    break;
            }
        });

        on.refreshBalance(() => {
            this.sendBalance();
        });
    }

    private sendBalance(): void {
        balanceService.ping().then((value) => {
            if (value !== null) {
                send.balanceUpdate(this.parseAmount(value));
            }
        });
    }

    private parseAmount(value: string | number) {
        return formatToCents(value);
    }

    public gameLoadStarted(): void {
        send.gameLoadStarted();
    }

    public gameLoadFinished(): void {
        send.gameLoadCompleted();
        this.sendBalance();
    }

    public runFinished(params: { balance: string; prevRunId: number }): void {
        setTimeout(() => {
            const betAmount = recentBetsSelectors.getRunBetAmount(params.prevRunId)(
                recentBetsStore.value,
            );
            const winAmount = recentBetsSelectors.getRunWinAmount(params.prevRunId)(
                recentBetsStore.value,
            );

            send.roundFinished({
                bet: this.parseAmount(betAmount),
                win: { win: this.parseAmount(winAmount) },
                balance: this.parseAmount(params.balance),
            });

            send.roundStarted({ balance: this.parseAmount(params.balance) });
        }, 300);
    }

    public balanceUpdate(value: string): void {
        send.balanceUpdate(this.parseAmount(value));
    }

    public updateSound(value: boolean): void {
        send.updateSettings({ sounds: value });
    }

    public exitGame(): void {
        send.exitGame();
    }

    public navigate(): void {
        send.navigate();
    }

    public registerModalsManager(manager: ModalsManager): void {
        this.modalsManager = manager;
    }

    public errorMessage(
        message: string,
        errorCode = WebIntegrationErrorCodes.GENERAL_UI_BOUNDARY,
    ): void {
        send.errorMessage({
            errorCode,
            message,
        });
    }
}
