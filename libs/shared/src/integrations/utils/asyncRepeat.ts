let cancelPreviousRepeat: () => void | null = null;

const createAsyncRepeat = (fn: () => Promise<void>, count: number): (() => void) => {
    let cancelled = false;

    const runAsyncRepeat = (currentCount: number): void => {
        if (currentCount === 0 || cancelled) {
            return;
        }

        fn().then(() => runAsyncRepeat(currentCount - 1));
    };

    runAsyncRepeat(count);

    return () => {
        cancelled = true;
    };
};

export const asyncRepeat = (fn: () => Promise<void>, count: number): void => {
    if (cancelPreviousRepeat) {
        cancelPreviousRepeat();
        cancelPreviousRepeat = null;
    }

    cancelPreviousRepeat = createAsyncRepeat(fn, count);
};
