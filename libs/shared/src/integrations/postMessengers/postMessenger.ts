import { PubSub } from '@bg-shared/utils/PubSub';
import { IPMEventPayload, IPMEventResponse } from '../shared/interfaces';

class PostMessenger {
    private readonly target: Window;

    private listeners = new PubSub();

    constructor(windowTarget: Window) {
        this.target = windowTarget;

        window.addEventListener('message', this.message, false);
    }

    private message = (event: MessageEvent): void => {
        const validatedMessage = this.validateResponse(event);

        if (!validatedMessage) {
            return;
        }

        const message = validatedMessage as IPMEventPayload;

        this.listeners.publish(message, message.type);
    };

    private validateResponse(event: MessageEvent): IPMEventPayload | boolean {
        if (!this.listeners.hasTypes() || typeof event.data !== 'string') {
            return false;
        }

        try {
            return JSON.parse(event.data);
        } catch (error) {
            return false;
        }
    }

    private validateRequest(type: string, payload?: unknown): string | boolean {
        try {
            return JSON.stringify({
                type,
                payload,
                windowName: window.name,
                // TODO: deprecated object key, can be removed only when all partners migrated
                data: {
                    ...(typeof payload === 'object' ? (payload as Record<string, unknown>) : {}),
                    windowName: window.name,
                },
            });
        } catch (error) {
            console.error('Something is wrong with post message request', error);
            return false;
        }
    }

    public emit(message: IPMEventPayload): void {
        const validated = this.validateRequest(message.type, message.payload);

        if (!validated) {
            return;
        }

        try {
            this.target.postMessage(validated, '*');

            if (window.top !== this.target) {
                window.top.postMessage(validated, '*');
            }
        } catch {}
    }

    public subscribe<T = unknown>(
        type: string,
        listener: ICallback<IPMEventResponse<T>>,
    ): ICallback {
        return this.listeners.subscribe<IPMEventResponse<T>>(listener, type);
    }

    public get<T = unknown, P = unknown>(
        type: string,
        payload?: P,
        rejectTimeout = 1000,
    ): Promise<T> {
        return new Promise((resolve, reject) => {
            const autoRejectTimeout = setTimeout(reject, rejectTimeout);

            const unsubscribe = this.subscribe<T>(type, (message) => {
                clearTimeout(autoRejectTimeout);
                unsubscribe();
                resolve(message.payload);
            });

            this.emit({
                type,
                payload,
            });
        });
    }
}

export const postMessenger = new PostMessenger(window.parent || window.top);
