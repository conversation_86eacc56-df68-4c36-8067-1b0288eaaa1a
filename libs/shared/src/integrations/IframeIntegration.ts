import { isObject } from 'lodash-unified';
import { uaParser } from '@betgames/bg-tools';
import { GameId } from '@bg-shared/enums/GameId';
import { analyticsApi } from '@bg-shared/business/Analytics';
import { sessionStore } from '@bg-shared/business/Session';
import {
    ContainerName,
    HeightManager,
    asyncRepeat,
    onElementHeightChange,
    isInIframe,
    isInViewport,
    getElementRect,
    iframeIntegrationFallback,
} from './utils';
import { EventTypes, IThemeStyles, IWindowData, UPDATE_HEIGHT_REPEAT } from './shared';
import { postMessenger } from './postMessengers';
import { OS_BROWSER_TOOLBAR_SIZE } from './constants';

const MIN_APP_HEIGHT = 500; // px
const DEFAULT_BROWSER_TOOLBAR_SIZE = 120; // px
const AUTO_CANCEL_WINDOW_DATA_TIMEOUT = 200; // ms

class IframeIntegration {
    private heightManager = new HeightManager();

    private latestHeight: number;

    private staticHeight = false;

    private newHeightModifier: (newHeight: number) => number;

    private parentWindowData: IWindowData;

    private toolbarSize: number;

    private getToolbarSize(): number {
        if (this.toolbarSize !== undefined) {
            return this.toolbarSize;
        }

        const os = uaParser.api.getOS().name;
        const browser = uaParser.browser.name;
        const { isTablet } = uaParser;

        // Trying to guess available height by user os and browser
        //   this is needed if partner is not using betgames.js loader
        //   or hasn't implemented new iframe window data events
        // "window.outerHeight" value is browser window size + tabs + url bar + bookmarks bar and can be different by browser

        this.toolbarSize = OS_BROWSER_TOOLBAR_SIZE[`${os}-${browser}${isTablet ? '-tablet' : ''}`];

        // if it's tablet then get try to non tablet version
        if (this.toolbarSize === undefined && isTablet) {
            this.toolbarSize = OS_BROWSER_TOOLBAR_SIZE[`${os}-${browser}`];
        }

        if (this.toolbarSize === undefined) {
            this.toolbarSize = DEFAULT_BROWSER_TOOLBAR_SIZE;
        }

        return this.toolbarSize;
    }

    private getParentWindowHeight(): number {
        return this.parentWindowData?.height ?? window.outerHeight - this.getToolbarSize();
    }

    private sendNewHeight(value?: number): void {
        let newHeight: number;

        if (this.staticHeight) {
            newHeight = this.getParentWindowHeight();
        } else {
            const height = Math.ceil(value || document.body.getBoundingClientRect().height);
            newHeight = Math.max(height, MIN_APP_HEIGHT);
        }

        if (this.newHeightModifier) {
            newHeight = this.newHeightModifier(newHeight);
        }

        if (!this.parentWindowData) {
            document.documentElement.style.setProperty(
                '--viewport-height',
                `${Math.min(
                    this.getParentWindowHeight(),
                    this.staticHeight ? window.innerHeight : Infinity,
                )}px`,
            );
        }

        postMessenger.emit({
            type: EventTypes.NEW_RESIZE,
            payload: {
                height: newHeight,
            },
        });

        /**
         * LEGACY: remove when partners migrated
         */
        window.top.postMessage(
            JSON.stringify({
                type: EventTypes.NEW_HEIGHT,
                windowName: window.name,
                height: newHeight,
            }),
            '*',
        );
    }

    private validateWindowData(windowData: IWindowData): boolean {
        return (
            (['width', 'height', 'scrollX', 'scrollY'] as (keyof IWindowData)[]).every(
                (key) => typeof windowData[key] === 'number',
            ) &&
            windowData.clientRect &&
            (['top', 'left'] as (keyof IWindowData['clientRect'])[]).every(
                (key) => typeof windowData.clientRect[key] === 'number',
            )
        );
    }

    public init(): void {
        const updateBodyHeight = (): void => {
            this.heightManager.addOrUpdate({
                name: ContainerName.BODY,
                height: document.body.offsetHeight,
            });

            this.updateHeight(this.getHeightManager().getBiggestHeight());
        };

        onElementHeightChange(document.body, updateBodyHeight);

        updateBodyHeight();

        if (isInIframe()) {
            postMessenger.subscribe<IWindowData>(EventTypes.WINDOW_DATA, (data) => {
                this.parentWindowData = data.payload;

                if (!this.validateWindowData(data.payload)) {
                    analyticsApi.trackIframeIntegrationFallback(
                        sessionStore.partnerCode,
                        'subscribeWindowData',
                        data.payload,
                    );
                }

                document.documentElement.style.setProperty(
                    '--viewport-height',
                    `${Math.min(
                        this.getParentWindowHeight(),
                        this.staticHeight
                            ? this.newHeightModifier(
                                  this.parentWindowData?.height ?? window.innerHeight,
                              )
                            : Infinity,
                    )}px`,
                );

                // for static height games when parent window height is resized need to send height update to update iframe height
                if (
                    this.staticHeight &&
                    this.parentWindowData?.height &&
                    this.parentWindowData.height !== window.innerHeight
                ) {
                    this.sendNewHeight(this.parentWindowData.height);
                }
            });

            postMessenger.emit({ type: EventTypes.SUBSCRIBE_WINDOW_RESIZE });
        }
    }

    public initThemeEditor(callback: ICallback): Promise<boolean> {
        return new Promise((resolve) => {
            const autoCancelTimeoutId = setTimeout(() => {
                resolve(true);

                console.warn(
                    'Theme editor auto-resolved, probably forgotten to send "setThemeStyles" message.',
                );
            }, 10000);

            postMessenger.subscribe<IThemeStyles>(EventTypes.SET_THEME, (data) => {
                const root = document.documentElement;

                if (!isObject(data.payload.themeData)) {
                    return;
                }

                root.removeAttribute('style');

                Object.entries(data.payload.themeData).forEach(([key, value]) => {
                    let validatedKey = key;

                    if (!/^--/.test(validatedKey)) {
                        validatedKey = `--${key}`;
                    }

                    root.style.setProperty(validatedKey, value);
                });

                if (callback) {
                    callback();
                }

                clearTimeout(autoCancelTimeoutId);

                resolve(true);
            });

            postMessenger.emit({
                type: EventTypes.THEME_READY,
            });
        });
    }

    public realityCheck(): void {
        if (!isInIframe()) {
            return;
        }

        postMessenger.emit({
            type: EventTypes.REALITY_CHECK,
        });
    }

    public balanceCheck(): void {
        if (!isInIframe()) {
            return;
        }

        postMessenger.emit({
            type: EventTypes.BALANCE_CHECK,
        });
    }

    public homeButtonClick(homeUrl: string): void {
        if (!isInIframe()) {
            return;
        }

        postMessenger.emit({
            type: EventTypes.HOME_BUTTON_CLICK,
            payload: { url: homeUrl },
        });
    }

    public fullscreen(active: boolean): void {
        if (!isInIframe()) {
            return;
        }

        postMessenger.emit({
            type: EventTypes.FULL_SCREEN,
            payload: { active },
        });
    }

    public gameNavigation(gameId: GameId | 'lobby'): void {
        if (!isInIframe()) {
            return;
        }

        postMessenger.emit({
            type: EventTypes.GAME_NAVIGATION,
            payload: { gameId },
        });
    }

    public lobbyOpened(): void {
        if (!isInIframe()) {
            return;
        }

        postMessenger.emit({
            type: EventTypes.LOBBY,
            payload: EventTypes.LOBBY,
        });
    }

    // scroll from top of the iframe
    public scrollToRelative(offset = 0): void {
        if (isInIframe()) {
            const fallback = (): void => {
                // TODO remove fallback when all partners migrated
                postMessenger.emit({
                    type: EventTypes.IFRAME_TOP,
                    payload: {
                        topPlus: offset,
                    },
                });
            };

            if (iframeIntegrationFallback.shouldUseWindowDataEventFallback()) {
                fallback();
                return;
            }

            postMessenger
                .get<IWindowData>(
                    EventTypes.WINDOW_DATA,
                    undefined,
                    AUTO_CANCEL_WINDOW_DATA_TIMEOUT,
                )
                .then((payload) => {
                    postMessenger.emit({
                        type: EventTypes.SCROLL_TO,
                        payload: {
                            scrollTo: payload.clientRect.top + payload.scrollY + offset,
                        },
                    });
                })
                .catch(() => {
                    iframeIntegrationFallback.countWindowDataEventRetry();

                    analyticsApi.trackIframeIntegrationFallback(
                        sessionStore.partnerCode,
                        EventTypes.IFRAME_TOP,
                    );
                    fallback();
                });
        } else {
            window.scroll(0, offset);
        }
    }

    // scroll from top of the partner window
    public scrollToAbsolute(position: number): void {
        if (isInIframe()) {
            postMessenger.emit({
                type: EventTypes.SCROLL_TO,
                payload: {
                    scrollTo: position,
                },
            });
        } else {
            window.scroll(0, position);
        }
    }

    public getPartnerScrollYOffset(): Promise<number> {
        return new Promise((resolve) => {
            if (isInIframe()) {
                const fallback = (): void => {
                    // TODO remove fallback when all partners migrated
                    postMessenger
                        .get<{ pageYOffset: number }>(EventTypes.PAGE_Y_OFFSET)
                        .then((payload) => {
                            resolve(payload.pageYOffset);
                        })
                        .catch(() => {
                            resolve(window.scrollY);
                        });
                };

                if (iframeIntegrationFallback.shouldUseWindowDataEventFallback()) {
                    fallback();
                    return;
                }

                postMessenger
                    .get<IWindowData>(
                        EventTypes.WINDOW_DATA,
                        undefined,
                        AUTO_CANCEL_WINDOW_DATA_TIMEOUT,
                    )
                    .then((payload) => {
                        resolve(payload.scrollY);
                    })
                    .catch(() => {
                        iframeIntegrationFallback.countWindowDataEventRetry();
                        fallback();

                        analyticsApi.trackIframeIntegrationFallback(
                            sessionStore.partnerCode,
                            EventTypes.PAGE_Y_OFFSET,
                        );
                    });
            } else {
                resolve(window.scrollY);
            }
        });
    }

    public scrollToVisible(element: HTMLElement): Promise<void> {
        return new Promise((resolve) => {
            const elementRect = getElementRect(element);

            if (isInIframe()) {
                postMessenger
                    .get<IWindowData>(
                        EventTypes.WINDOW_DATA,
                        undefined,
                        AUTO_CANCEL_WINDOW_DATA_TIMEOUT,
                    )
                    .then((payload) => {
                        const isVisible = isInViewport(elementRect, payload);

                        if (!isVisible) {
                            this.scrollToRelative(
                                elementRect.top + elementRect.height - payload.height,
                            );
                        }

                        resolve();
                    })
                    .catch(() => {
                        iframeIntegrationFallback.countWindowDataEventRetry();

                        analyticsApi.trackIframeIntegrationFallback(
                            sessionStore.partnerCode,
                            'isVisibleForParent',
                        );
                    });
            } else {
                window.scroll(0, elementRect.top + elementRect.height - window.innerHeight);
                resolve();
            }
        });
    }

    public updateHeight = (height?: number): void => {
        if (!isInIframe()) {
            return;
        }

        this.sendNewHeight(height);

        this.latestHeight = height;

        asyncRepeat(
            () =>
                new Promise((resolve) => {
                    setTimeout(() => {
                        this.sendNewHeight(this.latestHeight);
                        resolve();
                    }, 300);
                }),
            UPDATE_HEIGHT_REPEAT,
        );
    };

    public setStaticHeight(value: boolean) {
        this.staticHeight = value;
    }

    public setNewHeightModifier(
        newHeightModifier: typeof this.newHeightModifier = undefined,
    ): void {
        this.newHeightModifier = newHeightModifier;
    }

    public isStaticHeight(): boolean {
        return this.staticHeight;
    }

    public getHeightManager(): HeightManager {
        return this.heightManager;
    }
}

export const iframeIntegration = new IframeIntegration();
