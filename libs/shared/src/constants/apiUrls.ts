export const API_URLS = {
    TIPS: '/s/web/v1/tip',

    PAYIN: '/s/web/v1/betting/bet',

    BATCH: '/s/web/v1/betting/multi',

    BETTING_LIMITS: '/s/web/v1/odds/partner-odd-limits',

    BET_BY_RUN: '/s/web/v1/player/bet_by_run',

    AUTH: '/s/web/v1/player/auth',

    PLAYER_SETTINGS: '/s/web/v1/player/settings',

    AUTH_REVERSE: '/s/integration/v1/auth',

    INITIAL: '/s/web/v3/games/initial',

    VOD_INITIAL: '/s/web/v1/vod/initial',

    PROMOTION: '/s/web/v1/games/promotion',

    REFRESH_TOKEN: '/s/web/v1/player/refresh-token',

    RE_INITI_SESSION: '/s/web/v1/player/reinitialize',

    GET_BALANCE: '/s/web/v2/player/get-balance',

    REFRESH_BALANCE: '/s/web/v2/player/refresh-balance',

    ODDS: '/s/web/v1/odds/list',

    FAVORITE_ODD: '/s/web/v1/odds/favorite',

    POPULAR_ODDS: '/s/web/v1/odds/random',

    HOW_TO_PLAY: '/s/web/v2/static/how_to_play',

    RECENT_BETS: '/s/web/v1/player/recent_bets',

    RECENT_BETS_PER_RUN: '/s/web/v1/player/run/recent_bets',

    PLAYER_RECENT_ACTION: '/s/web/v1/player/recent-action',

    REPEATABLE_BETS: '/s/web/v1/betting/history/repeatable',

    BET_HISTORY_REPORT: '/s/web/v2/betting/history/bets',

    RESULTS_REPORT: '/s/web/v1/game/results',

    SINGLE_RUN_RESULTS_REPORT: '/s/web/v1/game/result',

    TOP_WON_AMOUNTS: '/s/web/v1/games/top_won_amounts/game',

    TRANSLATIONS: '/s/web/v1/static/translation',

    GAMES_OPTIONS: '/s/web/v1/game/options',

    GAME_PAYOUTS: '/s/web/v1/game/payouts',

    REPEAT_BETS: '/s/web/v1/betting/bet_repeat',

    STAGED_BETS: '/s/web/v1/betting/staged-bets',

    GAMIFICATION: {
        SETTINGS: '/s/web/v1/gamification/settings',

        CREATE_PROFILE: '/s/web/v1/gamification/create-profile',

        UPDATE_PROFILE: '/s/web/v1/gamification/update-profile',

        GET_AVATARS: '/s/web/v1/gamification/avatars',

        NICKNAME_AVAILABLE: '/s/web/v1/gamification/validate-nickname',

        BLOCK_PROFILE: '/s/web/v1/gamification/block-profile',

        UNBLOCK_PROFILE: '/s/web/v1/gamification/unblock-profile',

        DELETE_PROFILE: '/s/web/v1/gamification/delete-profile',

        HOW_TO_PLAY: '/s/web/v2/static/how_to_play/gamification',

        PURCHASE_HISTORY: '/s/web/v1/gamification/purchase-history',

        PURCHASE_ASSET: '/s/web/v1/gamification/purchase',
    },

    PLS_LAUNCH: '/s/web/v1/pls/launch',

    HINTER_ZIMMER_LAUNCH: '/s/web/v1/slot/launch',

    CHAT: {
        SAVE_NICKNAME: '/s/web/v1/nickname',
        AUTH: '/s/web/v1/chat/auth',
        SUPABASE_AUTH: '/chat/auth', // Same host
        SUPABASE_REGISTER: '/chat/register', // Same host
    },

    GIZA: {
        INITIAL: '/s/web/v1/giza/initial',
        BONUS_CHOICE: '/s/web/v1/giza/choice',
        CASH_OUT: '/s/web/v1/betting/giza-cash-out',
    },

    STARZLE: {
        CASH_OUT: '/s/web/v1/betting/starzle_cash_out',
    },

    SKYWARD_DELUXE: {
        WINBOARD: '/s/web/v1/winboard',
        LEADERBOARD: '/s/web/v1/leaderboard',
    },

    PROGRESSIVE_JACKPOT_WINNERS: '/s/web/v1/getJackpotWinners',
};
