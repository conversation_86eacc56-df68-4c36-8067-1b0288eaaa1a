import { useState, useEffect } from 'react';
import { useMounted } from '@betgames/bg-tools';
import { ModalName } from '@bg-shared/enums/ModalName';
import { ModalsManager } from '@bg-shared/contexts/ModalsManager';

export const useActiveModalName = (modalManager: ModalsManager): ModalName => {
    const mounted = useMounted();
    const [activeModal, setActiveModal] = useState<ModalName>(null);

    useEffect(
        () =>
            modalManager.subscribeOnModalsChange(() => {
                if (mounted.current) {
                    setActiveModal(modalManager.modals[0]?.name as ModalName);
                }
            }),
        [],
    );

    return activeModal;
};
