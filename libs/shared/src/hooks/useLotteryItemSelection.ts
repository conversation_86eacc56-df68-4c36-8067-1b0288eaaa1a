import { useState } from 'react';
import { ILotteryItem } from '@bg-shared/interfaces/ILotteryItem';

interface ILotteryItemSelection {
    (onChange?: (items: ILotteryItem[]) => void): {
        selectedItems: ILotteryItem[];
        setSelectedItems(items: ILotteryItem[]): void;
        toggleSectorSelection(item: ILotteryItem, sector: number): void;
    };
}

export const useLotteryItemSelection: ILotteryItemSelection = (onChange) => {
    const [selectedItems, setSelectedItems] = useState<ILotteryItem[]>([]);

    const toggleSectorSelection = (item: ILotteryItem, sector: number): void => {
        const newItems = [...selectedItems];

        newItems[sector] = newItems[sector] && newItems[sector].id === item.id ? undefined : item;

        setSelectedItems(newItems);

        if (onChange) {
            onChange(newItems);
        }
    };

    return {
        selectedItems,
        setSelectedItems,
        toggleSectorSelection,
    };
};
