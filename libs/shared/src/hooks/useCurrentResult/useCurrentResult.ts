import { useState, useRef, useEffect } from 'react';
import { useMounted, useAfterUpdate, useUnmount } from '@betgames/bg-tools';
import { currentResultService } from '@bg-shared/business/CurrentResult';
import { GameId } from '@bg-shared/enums/GameId';
import {
    IMappedRunItem,
    IMappedRunItemCallback,
} from '@bg-shared/business/CurrentResult/interfaces';

const ADDITIONAL_DELAY_BY_GAME: Partial<Record<GameId, number>> = {
    [GameId.LUCKY5]: 3000,
    [GameId.LUCKY6]: 3000,
    [GameId.LUCKY7]: 3000,
    [GameId.SHOVA52]: 500,
};

export const useCurrentResult = <T>(
    gameId: GameId,
    drawCode: string,
    delay: number,
    mapData: IMappedRunItemCallback<T>,
    disabled?: boolean,
): T[] => {
    const [results, setResults] = useState<IMappedRunItem<T>[]>([]);
    const runCodeRef = useRef<string>(drawCode);
    const resetTimeoutRef = useRef<number>(null);
    const mounted = useMounted();
    const additionalDelay = ADDITIONAL_DELAY_BY_GAME[gameId] ?? 0;

    useAfterUpdate(() => {
        resetTimeoutRef.current = setTimeout(() => {
            if (mounted.current) {
                runCodeRef.current = drawCode;
                setResults([]);
            }
        }, additionalDelay);
    }, [drawCode]);

    useUnmount(() => {
        clearTimeout(resetTimeoutRef.current);
    });

    useEffect(() => {
        if (disabled) {
            return undefined;
        }

        const timeouts: number[] = [];

        currentResultService.get(gameId, drawCode).then((data) => {
            if (!mounted.current) {
                return;
            }

            let videoSyncDelay = 0;

            if (data.items.length) {
                const lastItem = [...data.items].pop();
                const diffFromLive = data.emitTime - lastItem.infoTime;
                videoSyncDelay = Math.max(0, delay / 1000 - diffFromLive) * 1000;
            }

            timeouts.push(
                setTimeout(() => {
                    setResults(data.items.map(mapData));
                    timeouts.shift();
                }, videoSyncDelay + additionalDelay),
            );
        });

        const unsubscribe = currentResultService.subscribe(gameId, (response) => {
            timeouts.push(
                setTimeout(() => {
                    if (!mounted.current) {
                        return;
                    }

                    const mappedResponse = mapData(response);

                    setResults((state) => {
                        // If order interrupted re-fetch
                        if (mappedResponse.order > state.length + 1) {
                            currentResultService.get(gameId, runCodeRef.current).then((data) => {
                                if (mounted.current) {
                                    setResults(data.items.map(mapData));
                                }
                            });
                            return state;
                        }

                        // Skip the step if item.order <= current last item.order in state
                        if (mappedResponse.order <= state[state.length - 1]?.order) {
                            return state;
                        }

                        return [...state, mappedResponse];
                    });
                    timeouts.shift();
                }, delay + additionalDelay),
            );
        });

        return () => {
            unsubscribe();
            timeouts.forEach(clearTimeout);
        };
    }, [disabled]);

    return results.map((entry) => entry.item);
};
