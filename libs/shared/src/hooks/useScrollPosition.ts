import { RefObject, useEffect, useRef, useState } from 'react';
import { throttle } from 'lodash-unified';

const PAGE_AREA_THRESHOLD = 100;

// keep the scroll position while new chats are added
export const useScrollPosition = (
    containerRef: RefObject<HTMLDivElement>,
    messageCount: number,
    loading: boolean,
): {
    jumpToEnd: () => void;
} => {
    const [wasLoading, setWasLoading] = useState<boolean>(false);
    const [initialized, setInitialized] = useState<boolean>(false);

    const previousMessageCount = useRef<number>(messageCount);
    const lastScrollPosition = useRef<{
        scrollHeight: number; // The total scrollable height of the container
        scrollTop: number; // scroll position from the top
        isAtTheBottom: boolean;
    }>({
        scrollHeight: 0,
        scrollTop: 0,
        isAtTheBottom: false,
    });

    const jumpToEnd = () => {
        if (!containerRef.current) {
            return;
        }

        containerRef.current.scrollTo({
            top: containerRef.current.scrollHeight,
            behavior: 'instant',
        });
    };

    useEffect(() => {
        if (!containerRef.current) {
            return;
        }

        if (!initialized) {
            jumpToEnd();
            setInitialized(true);
        }
    }, [!!containerRef.current, initialized]);

    useEffect(() => {
        if (loading) {
            setWasLoading(loading);
        }
    }, [loading]);

    useEffect(() => {
        if (!containerRef.current) {
            return undefined;
        }

        const recordScrollPosition = throttle(() => {
            lastScrollPosition.current = {
                scrollHeight: containerRef.current.scrollHeight,
                scrollTop: containerRef.current.scrollTop,
                isAtTheBottom:
                    containerRef.current.scrollHeight - containerRef.current.scrollTop <
                    containerRef.current.clientHeight + PAGE_AREA_THRESHOLD,
            };
        }, 300);

        containerRef.current.addEventListener('scroll', recordScrollPosition);

        return () => {
            containerRef.current?.removeEventListener('scroll', recordScrollPosition);
        };
    }, [!!containerRef.current]);

    useEffect(() => {
        const chatContainer = containerRef.current;

        if (!chatContainer) {
            return;
        }

        if (messageCount > previousMessageCount.current) {
            const handleScroll = () => {
                const {
                    scrollHeight: lastScrollHeight,
                    scrollTop: lastScrollTop,
                    isAtTheBottom: wasAtTheBottom,
                } = lastScrollPosition.current;

                const { clientHeight, scrollTop, scrollHeight } = chatContainer;
                const heightDifference = scrollHeight - Math.abs(lastScrollHeight);

                let scrollTo;

                if (wasAtTheBottom) {
                    if (scrollHeight - lastScrollHeight > clientHeight) {
                        // if new message is too long, just scroll the message to the view
                        scrollTo = lastScrollTop + clientHeight / 2;
                    } else {
                        // if not, scroll to the bottom
                        scrollTo = scrollHeight;
                    }
                }

                if (
                    // if user is at top and loading old messages
                    (wasLoading && scrollHeight - scrollTop - clientHeight > PAGE_AREA_THRESHOLD) ||
                    !previousMessageCount.current
                ) {
                    scrollTo = lastScrollTop + heightDifference;
                }

                chatContainer.scrollTo({
                    top: scrollTo,
                    behavior: 'instant',
                });

                previousMessageCount.current = messageCount;
            };

            handleScroll();

            setWasLoading(false);
        }
    }, [messageCount]);

    return { jumpToEnd };
};
