import { useMemo } from 'react';
import { useStore } from '@betgames/bg-state-manager';
import { gamesMessagesEntity } from '@bg-shared/business/GamesMessages';
import { GameMessagePlacement } from '@bg-shared/business/GamesMessages/enums';

export const useTopNotification = (...messages: string[]): string => {
    const topMessage = useStore(gamesMessagesEntity.store(GameMessagePlacement.TOP))?.message;

    return useMemo(() => {
        // find returns first message which will be not null, undefined or '',
        // so the order of messages could be important.
        // Also there is a possibility to add option, which would indicate
        // which messages should be taken, or the first one of the stack, or tha last one.
        // If you need take the last one, messages.reverse() can be used.
        return [...messages, topMessage].find((message) => message);
    }, [messages.join('_'), topMessage]);
};
