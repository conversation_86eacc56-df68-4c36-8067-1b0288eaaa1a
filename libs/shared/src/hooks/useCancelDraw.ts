import { useState, useRef, useEffect } from 'react';
import { HOLD_CANCELLED_DRAW_MESSAGE_TIME } from '@bg-shared/constants/constants';
import { webSocketsRepository } from '@bg-shared/infrastructure/WebSockets';
import { t } from '@bg-shared/business/Translate';
import { CANCELLED_DRAW_CHANNEL_PREFIX } from '@bg-shared/constants/socketChannels';
import { GameId } from '@bg-shared/enums/GameId';

export const useCancelDraw = (gameId: GameId, runId: number | string): string => {
    const [message, setMessage] = useState<string>(null);
    const webSockets = webSocketsRepository;
    const runIdRef = useRef<number | string>(runId);

    runIdRef.current = runId;

    useEffect(() => {
        let timeout: number;

        const unsubscribe = webSockets.on(
            `${CANCELLED_DRAW_CHANNEL_PREFIX}${gameId}`,
            (data: { runId: string }) => {
                if (Number(data.runId) !== runIdRef.current) {
                    return;
                }

                setMessage(t.string('lottery_is_returned'));

                timeout = setTimeout(() => {
                    setMessage(null);
                }, HOLD_CANCELLED_DRAW_MESSAGE_TIME);
            },
        );

        return () => {
            unsubscribe();
            clearTimeout(timeout);
        };
    }, []);

    return message;
};
