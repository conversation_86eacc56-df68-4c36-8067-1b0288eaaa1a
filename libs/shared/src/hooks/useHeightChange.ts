import { MutableRefObject, useRef, useEffect } from 'react';
import { iframeIntegration } from '@bg-shared/integrations';
import { onElementHeightChange } from '@bg-shared/integrations/utils/onElementHeightChange';
import { IContainerName } from '@bg-shared/integrations/utils/heightManager';

export const useHeightChange = <Element extends HTMLDivElement>(
    containerName: IContainerName,
): MutableRefObject<Element> => {
    const containerRef = useRef<Element>();

    useEffect(() => {
        if (!containerRef.current) {
            return null;
        }

        const updateHeight = (): void => {
            if (!containerRef.current || iframeIntegration.isStaticHeight()) {
                return;
            }

            const { bottom } = containerRef.current.getBoundingClientRect();
            const { bottom: bodyBottom } = document.body.getBoundingClientRect();

            iframeIntegration.getHeightManager().addOrUpdate({
                name: containerName,
                height: bottom,
            });

            if (
                bottom > bodyBottom ||
                Math.max(bottom, bodyBottom) !== document.documentElement.offsetHeight
            ) {
                iframeIntegration.updateHeight(
                    iframeIntegration.getHeightManager().getBiggestHeight(),
                );
            }
        };

        const onOpenTimeout = setTimeout(updateHeight, 300);

        const disconnectHeightChange = onElementHeightChange(containerRef.current, updateHeight);

        return () => {
            clearTimeout(onOpenTimeout);
            setTimeout(() => {
                iframeIntegration.updateHeight(
                    iframeIntegration.getHeightManager().getBiggestHeight(),
                );
            }, 300);
            iframeIntegration.getHeightManager().remove(containerName);
            disconnectHeightChange();
        };
    }, []);

    return containerRef;
};
