import { useState, useEffect } from 'react';
import { ClassicGameDefinition } from '@bg-shared/utils/ClassicGameDefinition';
import { SHOW_RERUN_NOTIFICATION_TIME } from '@bg-shared/constants/constants';
import { t } from '@bg-shared/business/Translate';
import { webSocketsRepository } from '@bg-shared/infrastructure/WebSockets';
import { RERUN_CHANNEL } from '@bg-shared/constants/socketChannels';
import { GameId } from '@bg-shared/enums/GameId';

interface IRerun {
    lotteryEventId: string;
    runId: string;
}

export const useRerun = (gameId: GameId, currentRunId: number): string => {
    const [message, setMessage] = useState<string>(null);

    useEffect(() => {
        let timeout: number;

        const unsubscribe = webSocketsRepository.on<IRerun>(RERUN_CHANNEL, (data) => {
            if (Number(data.lotteryEventId) !== gameId || currentRunId !== Number(data.runId)) {
                return;
            }

            let rerunMessage: string;

            if (ClassicGameDefinition.isWheelType(gameId)) {
                rerunMessage = `${t.string('rerun_spin')} ${t.string('rerun_spin_rules')}`;
            } else if (ClassicGameDefinition.isDiceDuel(gameId)) {
                rerunMessage = `${t.string('rerun_roll')} ${t.string('rerun_roll_rules')}`;
            }

            if (!rerunMessage) {
                return;
            }

            setMessage(rerunMessage);

            timeout = setTimeout(() => {
                setMessage(null);
            }, SHOW_RERUN_NOTIFICATION_TIME);
        });

        return () => {
            unsubscribe();
            clearTimeout(timeout);
        };
    }, [currentRunId]);

    return message;
};
