import { GameId } from '@bg-shared/enums/GameId';
import { useLocation, useParams } from 'react-router-dom';
import { getMatchedGameId } from '@bg-shared/utils/getMatchedGameId';

export const useGameId = (): GameId => {
    const params = useParams<{ gameId: string }>();
    return Number(params.gameId);
};

export const useMatchedGameId = (): GameId => {
    const location = useLocation();
    return getMatchedGameId(location.pathname);
};
