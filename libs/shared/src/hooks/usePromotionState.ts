import { useStore } from '@betgames/bg-state-manager';
import { promotions, promotionsSelectors } from '@bg-shared/business/Promotions';
import {
    IPromotion,
    IPromotionCurrencyRate,
    IPromotionPayout,
} from '@bg-shared/business/Promotions/interfaces';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { MAX_ITEMS } from '@bg-shared/constants/constants';
import { GameId } from '@bg-shared/enums/GameId';
import { ISixPlusPokerCard } from '@bg-shared/interfaces/Draws/SixPlusPoker';

export interface IPromotionState {
    jackpot: IPromotion | null;
    jackpotRate: IPromotionCurrencyRate;
    payout: IPromotionPayout | null;
    showEligiblePlayers: boolean;
}

const gameJackpots: Partial<Record<GameId, string>> = {
    [GameId.SIX_PLUS_POKER]: 'pokerheadsupjackpot',
};

export const usePromotionState = (
    gameId: GameId,
    params?: {
        cards?: ISixPlusPokerCard[];
    },
): IPromotionState => {
    const jackpotType = gameJackpots[gameId] || null;
    const { partnerSettings } = partnerSettingsStore;
    const rates = useStore(promotions.store, promotionsSelectors.getPromotionsRates);
    const payout = useStore(promotions.store, (store) =>
        promotionsSelectors.getPromotionPayout(store, gameId),
    );
    const jackpot = useStore(promotions.store, (store) =>
        promotionsSelectors.getCurrentPromotion(store, gameId, jackpotType),
    );
    const items = params?.cards ?? [];
    const jackpotRate: IPromotionCurrencyRate = {
        precision: partnerSettings.currency.precision,
        template: partnerSettings.currency.template,
        code: partnerSettings.currency.code,
        rate:
            partnerSettings.currency.code.toLowerCase() === jackpot?.currencyCode.toLowerCase()
                ? '1'
                : jackpot && rates?.[jackpot.currencyCode],
    };

    const doNotShowEligiblePlayers = [0, MAX_ITEMS[gameId] ?? 0];

    return {
        jackpot,
        payout,
        jackpotRate,
        showEligiblePlayers: !doNotShowEligiblePlayers.includes(items.length),
    };
};
