import { useState, useRef, useEffect } from 'react';
import { useUnmount } from '@betgames/bg-tools';
import { t } from '@bg-shared/business/Translate';
import { type formatToUserCurrency } from '@bg-shared/utils/formatToUserCurrency';
import { promotions } from '@bg-shared/business/Promotions';
import { IPromotionPayout } from '@bg-shared/business/Promotions/interfaces';
import { INotification } from '@bg-shared/interfaces/INotification';

const EMPTY_MESSAGE = {
    place: '',
    message: '',
};

interface IState {
    showMessage: INotification;
    fadeClass: string;
}

export interface IBonusMessageProps {
    promoPayout: IPromotionPayout | null;
    currencyFormat: typeof formatToUserCurrency;
}

export const useBonusMessage = (props: IBonusMessageProps): IState => {
    const [message, setMessage] = useState<INotification>();
    const [fadeClass, setFadeClass] = useState<string>('fade-initial');
    const [lastPayout, setLastPayout] = useState<number>(null);
    const payoutKey = props.promoPayout ? props.promoPayout.id : null;
    const fadeOut = useRef<number>();
    const fadeInitial = useRef<number>();

    useEffect(() => {
        let newMessage: INotification;
        let callback = (): void => null;
        switch (true) {
            // If jackpot already shown, we must not show it again (exp. when switching games)
            case !!payoutKey &&
                lastPayout !== payoutKey &&
                !props.promoPayout.isShown &&
                props.promoPayout.promotionType.toLowerCase() !== 'cashback': // Temp fix to disable Jackpot message on Cashback payout
                callback = () => setLastPayout(payoutKey);

                promotions.store.updatePromotionPayout({
                    ...props.promoPayout,
                    isShown: true,
                });

                newMessage = {
                    place: t.string('jackpot_title'),
                    message: props.currencyFormat(props.promoPayout.amount, {
                        removeTrailingZeroes: true,
                    }),
                };
                break;
            default:
                newMessage = null;
        }

        if (!!newMessage && fadeClass === 'fade-initial') {
            setMessage(newMessage);
            setFadeClass('fade-in');
            fadeOut.current = setTimeout(() => {
                setFadeClass('fade-out');
                callback();
            }, 4000);
            fadeInitial.current = setTimeout(() => {
                setFadeClass('fade-initial');
            }, 4500);
        }
    }, [props.promoPayout, fadeClass]);

    useUnmount(() => {
        clearTimeout(fadeOut.current);
        clearTimeout(fadeInitial.current);
    });

    const showMessage = !message ? EMPTY_MESSAGE : message;

    return {
        showMessage,
        fadeClass,
    };
};
