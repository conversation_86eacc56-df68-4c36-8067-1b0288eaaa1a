import { useEffect } from 'react';
import { envConfig } from '@bg-shared/constants';
import { visibility } from '@betgames/bg-tools';
import { chatStore } from '@bg-shared/business';
import { useMobileView } from './useMobileView';

let lastChannelID: string = null;

export const useBeacon = () => {
    const { isDevEnv, chatConfig } = envConfig;

    const isMobile = useMobileView();
    const isClosed = window.closed;
    const isAmityChat = chatConfig?.apiKey;

    const sendBeacon = () => {
        const { userId, channelId } = chatStore.value;

        if (!userId || !channelId || lastChannelID === channelId) {
            return;
        }

        lastChannelID = channelId;

        navigator.sendBeacon(
            '/beacon',
            JSON.stringify({
                userId,
                channelId,
            }),
        );
    };

    if (isClosed && isAmityChat && !isDevEnv) {
        sendBeacon();
    }

    useEffect(() => {
        if (isDevEnv || !isAmityChat) {
            return undefined;
        }

        window.addEventListener('beforeunload', sendBeacon);
        if (isMobile) {
            window.addEventListener('pagehide', sendBeacon);
            visibility.setEvent((isHidden) => {
                if (isHidden) {
                    sendBeacon();
                } else {
                    lastChannelID = null;
                }
            });
        }

        return () => {
            window.removeEventListener('beforeunload', sendBeacon);
            if (isMobile) {
                window.removeEventListener('pagehide', sendBeacon);
            }

            chatStore.reset();
        };
    }, []);
};
