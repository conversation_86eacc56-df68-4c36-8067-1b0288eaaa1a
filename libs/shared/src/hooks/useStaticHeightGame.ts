import { useEffect } from 'react';
import { CONTAINER_WIDTH, STATIC_HEIGHT_CLASS } from '@bg-shared/constants/constants';
import { iframeIntegration } from '@bg-shared/integrations';
import { useMobileView } from '@bg-shared/hooks/useMobileView';
import { useModalsManager } from '@bg-shared/contexts/ModalsManager';

interface IProps {
    className?: string;
    staticHeightCallback?(): number;
}

export const useStaticHeightGame = (props: IProps = {}) => {
    const modalsManager = useModalsManager();
    const isMobileView = useMobileView(CONTAINER_WIDTH);

    useEffect(() => {
        document.body.classList.add(STATIC_HEIGHT_CLASS);
        if (props.className) {
            document.body.classList.add(props.className);
        }

        iframeIntegration.setStaticHeight(true);
        iframeIntegration.setNewHeightModifier((newHeight) => {
            if (!iframeIntegration.isStaticHeight()) {
                return newHeight;
            }

            if (props.staticHeightCallback) {
                return Math.min(props.staticHeightCallback() || newHeight, newHeight);
            }

            return newHeight;
        });
        iframeIntegration.scrollToRelative();

        return () => {
            document.body.classList.remove(STATIC_HEIGHT_CLASS, props.className);
            if (props.className) {
                document.body.classList.remove(props.className);
            }

            iframeIntegration.setStaticHeight(false);
            iframeIntegration.setNewHeightModifier();
        };
    }, []);

    useEffect(() => {
        if (!isMobileView) {
            iframeIntegration.setStaticHeight(true);
            return undefined;
        }

        const setStaticHeight = (): void => {
            iframeIntegration.setStaticHeight(!modalsManager.modals.length);
        };

        setStaticHeight();

        return modalsManager.subscribeOnModalsChange(setStaticHeight);
    }, [isMobileView]);
};
