import { animate, AnimationControls } from 'motion';
import { ICoordinates } from '@bg-shared/interfaces/ICoordinates';
import { Star } from '@bg-shared/utils/ExplodingStarsAnimation/Star';
import { EASING_OPTION } from '@bg-shared/constants/easingOption';

const MIN_STAR_AMOUNT = 30;
const MAX_STAR_AMOUNT = 40;

class ExplodingStarsAnimation {
    private stars: Star[];

    private finalValues: { totalDuration: number; x: number; y: number }[];

    private ctx: CanvasRenderingContext2D;

    private duration: number;

    public init(
        position: ICoordinates,
        finalPosition: ICoordinates,
        ctx: CanvasRenderingContext2D,
        duration: number,
        sizeRatio = 1,
    ) {
        this.ctx = ctx;
        this.duration = duration;
        const itemsCount = this.getRandomIntRange(MIN_STAR_AMOUNT, MAX_STAR_AMOUNT);
        const widthBase = Math.floor((position.x / 20) * sizeRatio);

        this.stars = Array(itemsCount).fill(
            new Star(
                position,
                this.getRandomIntRange(widthBase, widthBase * 2),
                this.getRandomIntRange(widthBase, widthBase * 3),
                sizeRatio,
            ),
        );

        // create final animation value for each star
        this.finalValues = this.stars.map((star) => ({
            x: star.x + this.getRandomRange(-finalPosition.x, finalPosition.x),
            y: star.y + this.getRandomRange(-finalPosition.y, finalPosition.y),
            totalDuration: this.getRandomRange(duration * 0.5, duration) / duration,
        }));
    }

    public run(delay = 0): AnimationControls {
        // progress (0 - 1) adjusted to individual star animation duration
        let adjustedProgress;
        const animation = animate(
            (progress: number) => {
                this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);

                // needed for delay to work;
                if (progress === 0) {
                    return;
                }

                this.stars.forEach((star, index) => {
                    adjustedProgress = Math.min(
                        this.scaleToRange(progress, 0, this.finalValues[index].totalDuration, 0, 1),
                        1,
                    );
                    star.draw(
                        this.ctx,
                        this.scaleToRange(
                            adjustedProgress,
                            0,
                            1,
                            star.x,
                            this.finalValues[index].x,
                        ),
                        this.scaleToRange(
                            adjustedProgress,
                            0,
                            1,
                            star.y,
                            this.finalValues[index].y,
                        ),
                        this.scaleToRange(adjustedProgress, 0, 1, star.radius, 0),
                        // delay "opacity" by 70% of star duration
                        this.scaleToRange(adjustedProgress, 0.7, 1, star.opacity, 0),
                    );
                });
            },
            {
                duration: this.duration,
                delay,
                easing: EASING_OPTION.EASE_OUT_EXPO,
            },
        );

        animation.finished.then(() => {
            this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        });

        return animation;
    }

    private getRandomRange(min: number, max: number): number {
        return Math.random() * (max - min) + min;
    }

    private getRandomIntRange(min: number, max: number): number {
        return Math.floor(this.getRandomRange(min, max));
    }

    private scaleToRange(
        value: number,
        inMin: number,
        inMax: number,
        outMin: number,
        outMax: number,
    ): number {
        return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;
    }
}

export const explodingStarsAnimation = new ExplodingStarsAnimation();
