export const debounceAsync = <Response, Params>(
    callback: (params: Params) => Promise<Response>,
    wait: number,
): ((params?: Params) => Promise<Response>) => {
    let timeoutId: number | null = null;

    return (params) => {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        return new Promise<Response>((resolve) => {
            const timeoutPromise = new Promise<void>((tResolve) => {
                timeoutId = setTimeout(tResolve, wait);
            });

            timeoutPromise.then(async () => {
                resolve(await callback(params));
            });
        });
    };
};
