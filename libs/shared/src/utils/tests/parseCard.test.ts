import { parseCard } from '../parseCard';

describe('Utils: parseCard', () => {
    it('should parse card', () => {
        expect(parseCard('2d')).toStrictEqual({ value: '2', suit: 'diamonds' });
        expect(parseCard('Th')).toStrictEqual({ value: '10', suit: 'hearts' });
        expect(parseCard('5z')).toStrictEqual({ value: '5', suit: 'all' });
        expect(parseCard('j')).toStrictEqual({ value: 'j', suit: 'all' });
        expect(parseCard('')).toStrictEqual({ value: null, suit: null });
        expect(parseCard(null)).toStrictEqual({ value: null, suit: null });
        expect(parseCard(undefined)).toStrictEqual({ value: null, suit: null });
    });
});
