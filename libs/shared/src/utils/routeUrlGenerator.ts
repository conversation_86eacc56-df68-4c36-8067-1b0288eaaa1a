import { ClassicGameDefinition } from '@bg-shared/utils/ClassicGameDefinition';
import { getTodayDate } from '@bg-shared/utils/getTodayDate';
import { sessionStore } from '@bg-shared/business/Session';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { BetType } from '@bg-shared/enums/BetType';
import { GameId } from '@bg-shared/enums/GameId';

class RouteUrlGenerator {
    private readonly routesNames = {
        AUTH: 'auth',
        INTEGRATION: 'integration',
        BETS: 'bets',
        SUBSCRIPTION: 'subscription',
        RESULTS: 'results',
        HOW_TO_PLAY: 'how_to_play',
        GAME: 'game',
        RND: 'RND',
        VIDEO: 'video',
        LOBBY: 'lobby',
        NEAREST: 'nearest',
    };

    public readonly lobbyGameId = -1;

    private static getGameIdByPageName(pageName: string): number {
        if (pageName === ClassicGameDefinition.GAMES_NAMES[GameId.POKER]) {
            return GameId.POKER;
        }

        const gameId = Number(pageName);

        if (!(gameId in GameId)) {
            return 0;
        }

        return gameId;
    }

    public get routes() {
        return this.routesNames;
    }

    public getBetsUrl(params?: { date?: string; type?: BetType; page?: number }): string {
        const date = params?.date ?? getTodayDate(sessionStore.timezone);
        const page = params?.page ?? 1;
        const type = params?.type ?? BetType.SINGLE;

        return `/${this.routesNames.BETS}/${date}/${page}/${type}`;
    }

    public getResultsUrl(params?: {
        date?: string;
        page?: number;
        gameId?: string;
        run?: string;
    }): string {
        const date = params?.date ?? getTodayDate(sessionStore.timezone);
        const page = params?.page ?? 1;
        const gameId = params?.gameId ?? 0;
        const run = params?.run ?? '';

        return `/${this.routesNames.RESULTS}/${date}/${page}/${gameId}/${run}`;
    }

    public getGameUrl(gameId?: GameId | typeof this.lobbyGameId): string {
        if (Number(gameId) === this.lobbyGameId) {
            return this.getLobbyUrl();
        }

        let validGameId = gameId || sessionStore.defaultGame;

        if (!partnerSettingsStore.availableGamesIds.includes(validGameId)) {
            [validGameId] = partnerSettingsStore.availableGamesIds;
        }

        return `/${this.routesNames.GAME}/${validGameId}`;
    }

    public getHowToPlayUrl(gameId?: GameId): string {
        return `/${this.routesNames.HOW_TO_PLAY}/${
            gameId ?? partnerSettingsStore.availableGamesIds[0]
        }`;
    }

    public getArchiveUrl(runId: string): string {
        return `/${this.routesNames.VIDEO}/${runId}`;
    }

    public getLobbyUrl(): string {
        return `/${this.routesNames.LOBBY}`;
    }

    public getAuthRedirectUrl(route: string, params: { gameId?: number; runId?: string }): string {
        if (route === this.routesNames.RESULTS) {
            return this.getResultsUrl();
        }

        if (route === this.routesNames.LOBBY || Number(route) === this.lobbyGameId) {
            return this.getLobbyUrl();
        }

        if (route === this.routesNames.VIDEO && params.runId) {
            return this.getArchiveUrl(params.runId);
        }

        const parsedGameId = RouteUrlGenerator.getGameIdByPageName(route);

        return this.getGameUrl(parsedGameId);
    }
}

export const routeUrlGenerator = new RouteUrlGenerator();
