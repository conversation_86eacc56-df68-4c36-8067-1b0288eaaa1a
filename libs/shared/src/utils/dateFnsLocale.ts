// curl https://crms.betgames.tv/api/v1/export/languages | jq | grep '"code"' | awk '{print $2}'
// Update together with date-fns library upgrades

import { setDefaultOptions } from 'date-fns';
import type { Locale } from 'date-fns';

// Type for date-fns locale modules - they export both named exports and default export
// The default export is the same Locale object as the named export
type DateFnsLocaleModule = Record<string, unknown>;

type ElementType<T> = T extends Array<infer U> ? U : T extends ReadonlyArray<infer U> ? U : never;

// https://github.com/date-fns/date-fns/issues/3660
const DATE_FNS_LOCALE_MAP = {
    af: () => /* webpackMode: "lazy", webpackChunkName: "af':" */ import('date-fns/locale/af'),
    ar: () => /* webpackMode: "lazy", webpackChunkName: "ar':" */ import('date-fns/locale/ar'),
    'ar-DZ': () /* webpackMode: "lazy", webpackChunkName: "ar-DZ" */ =>
        import('date-fns/locale/ar-DZ'),
    'ar-EG': () /* webpackMode: "lazy", webpackChunkName: "ar-EG" */ =>
        import('date-fns/locale/ar-EG'),
    'ar-MA': () /* webpackMode: "lazy", webpackChunkName: "ar-MA" */ =>
        import('date-fns/locale/ar-MA'),
    'ar-SA': () /* webpackMode: "lazy", webpackChunkName: "ar-SA" */ =>
        import('date-fns/locale/ar-SA'),
    'ar-TN': () /* webpackMode: "lazy", webpackChunkName: "ar-TN" */ =>
        import('date-fns/locale/ar-TN'),
    az: () => /* webpackMode: "lazy", webpackChunkName: "az':" */ import('date-fns/locale/az'),
    be: () => /* webpackMode: "lazy", webpackChunkName: "be':" */ import('date-fns/locale/be'),
    'be-tarask': () /* webpackMode: "lazy", webpackChunkName: "be-tarask" */ =>
        import('date-fns/locale/be-tarask'),
    bg: () => /* webpackMode: "lazy", webpackChunkName: "bg':" */ import('date-fns/locale/bg'),
    bn: () => /* webpackMode: "lazy", webpackChunkName: "bn':" */ import('date-fns/locale/bn'),
    bs: () => /* webpackMode: "lazy", webpackChunkName: "bs':" */ import('date-fns/locale/bs'),
    ca: () => /* webpackMode: "lazy", webpackChunkName: "ca':" */ import('date-fns/locale/ca'),
    ckb: () => /* webpackMode: "lazy", webpackChunkName: "ckb':" */ import('date-fns/locale/ckb'),
    cs: () => /* webpackMode: "lazy", webpackChunkName: "cs':" */ import('date-fns/locale/cs'),
    cy: () => /* webpackMode: "lazy", webpackChunkName: "cy':" */ import('date-fns/locale/cy'),
    da: () => /* webpackMode: "lazy", webpackChunkName: "da':" */ import('date-fns/locale/da'),
    de: () => /* webpackMode: "lazy", webpackChunkName: "de':" */ import('date-fns/locale/de'),
    'de-AT': () /* webpackMode: "lazy", webpackChunkName: "de-AT" */ =>
        import('date-fns/locale/de-AT'),
    el: () => /* webpackMode: "lazy", webpackChunkName: "el':" */ import('date-fns/locale/el'),
    'en-AU': () /* webpackMode: "lazy", webpackChunkName: "en-AU" */ =>
        import('date-fns/locale/en-AU'),
    'en-CA': () /* webpackMode: "lazy", webpackChunkName: "en-CA" */ =>
        import('date-fns/locale/en-CA'),
    'en-GB': () /* webpackMode: "lazy", webpackChunkName: "en-GB" */ =>
        import('date-fns/locale/en-GB'),
    'en-IE': () /* webpackMode: "lazy", webpackChunkName: "en-IE" */ =>
        import('date-fns/locale/en-IE'),
    'en-IN': () /* webpackMode: "lazy", webpackChunkName: "en-IN" */ =>
        import('date-fns/locale/en-IN'),
    'en-NZ': () /* webpackMode: "lazy", webpackChunkName: "en-NZ" */ =>
        import('date-fns/locale/en-NZ'),
    'en-US': () /* webpackMode: "lazy", webpackChunkName: "en-US" */ =>
        import('date-fns/locale/en-US'),
    'en-ZA': () /* webpackMode: "lazy", webpackChunkName: "en-ZA" */ =>
        import('date-fns/locale/en-ZA'),
    eo: () => /* webpackMode: "lazy", webpackChunkName: "eo':" */ import('date-fns/locale/eo'),
    es: () => /* webpackMode: "lazy", webpackChunkName: "es':" */ import('date-fns/locale/es'),
    et: () => /* webpackMode: "lazy", webpackChunkName: "et':" */ import('date-fns/locale/et'),
    eu: () => /* webpackMode: "lazy", webpackChunkName: "eu':" */ import('date-fns/locale/eu'),
    'fa-IR': () /* webpackMode: "lazy", webpackChunkName: "fa-IR" */ =>
        import('date-fns/locale/fa-IR'),
    fi: () => /* webpackMode: "lazy", webpackChunkName: "fi':" */ import('date-fns/locale/fi'),
    fr: () => /* webpackMode: "lazy", webpackChunkName: "fr':" */ import('date-fns/locale/fr'),
    'fr-CA': () /* webpackMode: "lazy", webpackChunkName: "fr-CA" */ =>
        import('date-fns/locale/fr-CA'),
    'fr-CH': () /* webpackMode: "lazy", webpackChunkName: "fr-CH" */ =>
        import('date-fns/locale/fr-CH'),
    fy: () => /* webpackMode: "lazy", webpackChunkName: "fy':" */ import('date-fns/locale/fy'),
    gd: () => /* webpackMode: "lazy", webpackChunkName: "gd':" */ import('date-fns/locale/gd'),
    gl: () => /* webpackMode: "lazy", webpackChunkName: "gl':" */ import('date-fns/locale/gl'),
    gu: () => /* webpackMode: "lazy", webpackChunkName: "gu':" */ import('date-fns/locale/gu'),
    he: () => /* webpackMode: "lazy", webpackChunkName: "he':" */ import('date-fns/locale/he'),
    hi: () => /* webpackMode: "lazy", webpackChunkName: "hi':" */ import('date-fns/locale/hi'),
    hr: () => /* webpackMode: "lazy", webpackChunkName: "hr':" */ import('date-fns/locale/hr'),
    ht: () => /* webpackMode: "lazy", webpackChunkName: "ht':" */ import('date-fns/locale/ht'),
    hu: () => /* webpackMode: "lazy", webpackChunkName: "hu':" */ import('date-fns/locale/hu'),
    hy: () => /* webpackMode: "lazy", webpackChunkName: "hy':" */ import('date-fns/locale/hy'),
    id: () => /* webpackMode: "lazy", webpackChunkName: "id':" */ import('date-fns/locale/id'),
    is: () => /* webpackMode: "lazy", webpackChunkName: "is':" */ import('date-fns/locale/is'),
    it: () => /* webpackMode: "lazy", webpackChunkName: "it':" */ import('date-fns/locale/it'),
    'it-CH': () /* webpackMode: "lazy", webpackChunkName: "it-CH" */ =>
        import('date-fns/locale/it-CH'),
    ja: () => /* webpackMode: "lazy", webpackChunkName: "ja':" */ import('date-fns/locale/ja'),
    'ja-Hira': () /* webpackMode: "lazy", webpackChunkName: "ja-Hira" */ =>
        import('date-fns/locale/ja-Hira'),
    ka: () => /* webpackMode: "lazy", webpackChunkName: "ka':" */ import('date-fns/locale/ka'),
    kk: () => /* webpackMode: "lazy", webpackChunkName: "kk':" */ import('date-fns/locale/kk'),
    km: () => /* webpackMode: "lazy", webpackChunkName: "km':" */ import('date-fns/locale/km'),
    kn: () => /* webpackMode: "lazy", webpackChunkName: "kn':" */ import('date-fns/locale/kn'),
    ko: () => /* webpackMode: "lazy", webpackChunkName: "ko':" */ import('date-fns/locale/ko'),
    lb: () => /* webpackMode: "lazy", webpackChunkName: "lb':" */ import('date-fns/locale/lb'),
    lt: () => /* webpackMode: "lazy", webpackChunkName: "lt':" */ import('date-fns/locale/lt'),
    lv: () => /* webpackMode: "lazy", webpackChunkName: "lv':" */ import('date-fns/locale/lv'),
    mk: () => /* webpackMode: "lazy", webpackChunkName: "mk':" */ import('date-fns/locale/mk'),
    mn: () => /* webpackMode: "lazy", webpackChunkName: "mn':" */ import('date-fns/locale/mn'),
    ms: () => /* webpackMode: "lazy", webpackChunkName: "ms':" */ import('date-fns/locale/ms'),
    mt: () => /* webpackMode: "lazy", webpackChunkName: "mt':" */ import('date-fns/locale/mt'),
    nb: () => /* webpackMode: "lazy", webpackChunkName: "nb':" */ import('date-fns/locale/nb'),
    nl: () => /* webpackMode: "lazy", webpackChunkName: "nl':" */ import('date-fns/locale/nl'),
    'nl-BE': () /* webpackMode: "lazy", webpackChunkName: "nl-BE" */ =>
        import('date-fns/locale/nl-BE'),
    nn: () => /* webpackMode: "lazy", webpackChunkName: "nn':" */ import('date-fns/locale/nn'),
    oc: () => /* webpackMode: "lazy", webpackChunkName: "oc':" */ import('date-fns/locale/oc'),
    pl: () => /* webpackMode: "lazy", webpackChunkName: "pl':" */ import('date-fns/locale/pl'),
    pt: () => /* webpackMode: "lazy", webpackChunkName: "pt':" */ import('date-fns/locale/pt'),
    'pt-BR': () /* webpackMode: "lazy", webpackChunkName: "pt-BR" */ =>
        import('date-fns/locale/pt-BR'),
    ro: () => /* webpackMode: "lazy", webpackChunkName: "ro':" */ import('date-fns/locale/ro'),
    ru: () => /* webpackMode: "lazy", webpackChunkName: "ru':" */ import('date-fns/locale/ru'),
    se: () => /* webpackMode: "lazy", webpackChunkName: "se':" */ import('date-fns/locale/se'),
    sk: () => /* webpackMode: "lazy", webpackChunkName: "sk':" */ import('date-fns/locale/sk'),
    sl: () => /* webpackMode: "lazy", webpackChunkName: "sl':" */ import('date-fns/locale/sl'),
    sq: () => /* webpackMode: "lazy", webpackChunkName: "sq':" */ import('date-fns/locale/sq'),
    sr: () => /* webpackMode: "lazy", webpackChunkName: "sr':" */ import('date-fns/locale/sr'),
    'sr-Latn': () /* webpackMode: "lazy", webpackChunkName: "sr-Latn" */ =>
        import('date-fns/locale/sr-Latn'),
    sv: () => /* webpackMode: "lazy", webpackChunkName: "sv':" */ import('date-fns/locale/sv'),
    ta: () => /* webpackMode: "lazy", webpackChunkName: "ta':" */ import('date-fns/locale/ta'),
    te: () => /* webpackMode: "lazy", webpackChunkName: "te':" */ import('date-fns/locale/te'),
    th: () => /* webpackMode: "lazy", webpackChunkName: "th':" */ import('date-fns/locale/th'),
    tr: () => /* webpackMode: "lazy", webpackChunkName: "tr':" */ import('date-fns/locale/tr'),
    ug: () => /* webpackMode: "lazy", webpackChunkName: "ug':" */ import('date-fns/locale/ug'),
    uk: () => /* webpackMode: "lazy", webpackChunkName: "uk':" */ import('date-fns/locale/uk'),
    uz: () => /* webpackMode: "lazy", webpackChunkName: "uz':" */ import('date-fns/locale/uz'),
    'uz-Cyrl': () /* webpackMode: "lazy", webpackChunkName: "uz-Cyrl" */ =>
        import('date-fns/locale/uz-Cyrl'),
    vi: () => /* webpackMode: "lazy", webpackChunkName: "vi':" */ import('date-fns/locale/vi'),
    'zh-CN': () /* webpackMode: "lazy", webpackChunkName: "zh-CN" */ =>
        import('date-fns/locale/zh-CN'),
    'zh-HK': () /* webpackMode: "lazy", webpackChunkName: "zh-HK" */ =>
        import('date-fns/locale/zh-HK'),
    'zh-TW': () /* webpackMode: "lazy", webpackChunkName: "zh-TW" */ =>
        import('date-fns/locale/zh-TW'),
};

const DATE_FNS_LOCALES_LIST = Object.keys(DATE_FNS_LOCALE_MAP);

export class DateFnsLocale {
    private readonly BG_LOCALES_MAP: Record<string, string> = {
        ee: 'et', // Estonian
        dk: 'da', // Danish
        gr: 'el', // Greek
        ge: 'ka', // Georgian
        xr: 'ar', // Arabic
        bh: 'bg', // Bulgarian
        cg: 'fr', // French (Congo)
        xl: 'es', // Spanish (Latin America)
        il: 'he', // Hebrew (he-IL)
        zh: 'zh-TW', // Chinese
        zh_hant: 'zh-TW', // Chinese
        ar: 'ar-MA',
        lru: 'ru', // Lotto Russian
        fa: 'fa-IR', // Persian/Farsi
        no: 'nn', // Norwegian
        'pt-br': 'pt-BR', // Portuguese
        pt_br: 'pt-BR', // Portuguese
    };

    private appLocale: Locale;

    public get locale(): Locale {
        if (!this.appLocale) {
            throw new Error('You probably forgot to use "setup" and provide locale');
        }

        return this.appLocale;
    }

    public async setup(locale: string): Promise<void> {
        const trimmedLocale = locale.replace(/\d+$/, '');
        const sliced = locale.slice(0, 2);
        const code = (this.BG_LOCALES_MAP[trimmedLocale] ||
            this.BG_LOCALES_MAP[sliced] ||
            sliced) as ElementType<typeof DATE_FNS_LOCALES_LIST>;

        let localeModule: DateFnsLocaleModule;

        try {
            localeModule = await DATE_FNS_LOCALE_MAP[code]();
        } catch {
            localeModule = await DATE_FNS_LOCALE_MAP['en-GB']();
        }

        try {
            this.appLocale = this.extractLocaleFromModule(localeModule);
            setDefaultOptions({ locale: this.appLocale });
        } catch (error) {
            console.warn(`Failed to extract locale from module for code "${code}":`, error);
        }
    }

    private extractLocaleFromModule(localeModule: DateFnsLocaleModule): Locale {
        // Handle null or undefined module
        if (!localeModule || typeof localeModule !== 'object') {
            throw new Error('Invalid locale module: module is null, undefined, or not an object');
        }

        // Date-fns locale modules export the locale as named exports and default export
        // The locale object should have a 'code' property
        const localeKeys = Object.keys(localeModule) as string[];

        if (localeKeys.length === 0) {
            throw new Error('Invalid locale module: module has no exports');
        }

        // Look for locale objects (should have 'code' property)
        const localeObjects: Array<{ key: string; value: Locale }> = [];

        localeKeys.forEach((key) => {
            const value = localeModule[key];
            // Check if this looks like a locale object (has code property)
            if (value && typeof value === 'object' && 'code' in value) {
                localeObjects.push({ key, value: value as Locale });
            }
        });

        if (localeObjects.length === 0) {
            throw new Error(
                `Could not find valid locale object in date-fns module. ` +
                    `Available keys: ${localeKeys.join(', ')}. ` +
                    `Expected at least one object with a 'code' property.`,
            );
        }

        // Return the first valid locale object found
        return localeObjects[0].value;
    }
}

export const dateFnsLocale = new DateFnsLocale();
