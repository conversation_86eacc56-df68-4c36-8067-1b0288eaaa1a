import { taxes } from '@betgames/bg-tools';
import { GameId } from '@bg-shared/enums/GameId';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { sessionStore } from '@bg-shared/business/Session';

export const configureTax = (gameId?: GameId) => {
    if (partnerSettingsStore.partnerSettings.taxes) {
        let taxEnabled = partnerSettingsStore.partnerSettings.taxes?.enabled;

        if (gameId) {
            taxEnabled = taxEnabled && partnerSettingsStore.isTaxEnabledForGame(gameId);
        }

        taxes.configure(
            taxEnabled && partnerSettingsStore.partnerSettings.taxes,
            sessionStore.session.currency || partnerSettingsStore.partnerSettings.currency,
        );
    }
};
