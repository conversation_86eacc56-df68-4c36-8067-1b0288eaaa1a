import { CardSuit } from '@bg-shared/enums/CardSuit';
import { CardSuitShort } from '@bg-shared/enums/CardSuitShort';
import { formatCardValue } from '@bg-shared/utils/formatCardValue';
import { ICard } from '@bg-shared/interfaces/ICard';

const suits = {
    [CardSuitShort.SPADES]: CardSuit.SPADES,
    [CardSuitShort.CLUBS]: CardSuit.CLUBS,
    [CardSuitShort.HEARTS]: CardSuit.HEARTS,
    [CardSuitShort.DIAMONDS]: CardSuit.DIAMONDS,
};

export const parseCard = (cardString: string): ICard => {
    if (!cardString?.length) {
        return { value: null, suit: null };
    }

    const value = formatCardValue(cardString.charAt(0));
    const suit = cardString.charAt(1).toLowerCase() as CardSuitShort;

    return { value, suit: suits[suit] ?? CardSuit.ALL };
};
