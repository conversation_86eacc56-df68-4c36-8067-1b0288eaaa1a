import { convertUTCToTimezone } from '@betgames/bg-tools';
import { sessionStore } from '@bg-shared/business/Session/Session.store';
import { convertToPartnerDateTime } from './convertToPartnerDateTime';

export const getRelativeTime = (date: string): string => {
    if (!date) {
        return null;
    }

    const utcTimestamp = new Date(date).getTime();

    const currentPartnerTime = convertUTCToTimezone(Date.now(), sessionStore.timezone);
    const diff = currentPartnerTime - utcTimestamp;

    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (minutes < 1) {
        return 'Just now';
    }
    if (minutes < 60) {
        return `${minutes} min ago`;
    }
    if (hours < 24) {
        return `${hours}h ago`;
    }
    if (days < 7) {
        return `${days}d ago`;
    }

    return convertToPartnerDateTime(utcTimestamp, sessionStore.timezone, sessionStore.language);
};
