export type IType = string | number | symbol;

export class PubSub<Payload> {
    private readonly defaultType = Symbol('default');

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private readonly listeners = new Map<IType, Set<ICallback<Payload | any>>>();

    public subscribe<T extends Payload>(
        listener: ICallback<T>,
        type: IType = this.defaultType,
    ): () => void {
        if (!this.listeners.has(type)) {
            this.listeners.set(type, new Set());
        }

        const typeListeners = this.listeners.get(type);

        typeListeners.add(listener);

        return () => {
            typeListeners.delete(listener);

            if (!typeListeners.size) {
                this.listeners.delete(type);
            }
        };
    }

    public publish(payload?: Payload, type: IType = this.defaultType): void {
        const typeListeners = this.listeners.get(type);

        if (!typeListeners) {
            return;
        }

        typeListeners.forEach((listener) => {
            listener(payload);
        });
    }

    public hasTypes(): boolean {
        return !!this.listeners.size;
    }
}
