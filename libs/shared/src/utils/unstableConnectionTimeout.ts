import { gamesMessagesEntity } from '@bg-shared/business/GamesMessages';
import { t } from '@bg-shared/business/Translate';
import { GameMessagePlacement } from '@bg-shared/business/GamesMessages/enums';

const CONNECTION_UNSTABLE_GAME_MESSAGE_TIMEOUT = 5000; // ms
const CONNECTION_TIMEOUT = 1500; // ms, timeout treated that user is offline

class UnstableConnectionTimeout {
    private timeoutId: number;

    private connectionUnstable = false;

    private connectionUnstableShown = false;

    public isUnstable(): boolean {
        return this.connectionUnstable;
    }

    public setStable(): void {
        this.connectionUnstable = false;
    }

    public start(): void {
        if (this.connectionUnstableShown) {
            return;
        }

        this.clear();

        this.timeoutId = setTimeout(() => {
            gamesMessagesEntity.addToQueue(
                {
                    message: t.string('unstable_connection'),
                    timeout: CONNECTION_UNSTABLE_GAME_MESSAGE_TIMEOUT,
                },
                GameMessagePlacement.TOP,
            );

            this.connectionUnstable = true;
            this.connectionUnstableShown = true;
        }, CONNECTION_TIMEOUT);
    }

    public clear(): void {
        clearTimeout(this.timeoutId);
    }

    public reset(): void {
        this.connectionUnstable = false;
        this.connectionUnstableShown = false;
    }
}

export const unstableConnectionTimeout = new UnstableConnectionTimeout();
