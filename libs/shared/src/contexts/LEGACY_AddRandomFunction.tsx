import { createContext, Props<PERSON>ith<PERSON>hildren, JS<PERSON>, useRef, useCallback, useContext } from 'react';
import { GamesContexts } from '@bg-shared/contexts/Games';
import { GameId } from '@bg-shared/enums/GameId';
import { bettingOptionsService } from '@bg-shared/business/BettingOptions';
import { selectedGames } from '@bg-shared/business/SelectedGames';

type IRandomFunction = (gamesIds: GameId[]) => void;

const Context = createContext<IRandomFunction>(undefined);

export const AddRandomFunctionProvider = (props: PropsWithChildren): JSX.Element => {
    const games = GamesContexts.useGamesDataForBetslip();
    const topOptionsRef = useRef<Record<GameId, number[]>>(null);

    const dispatchNewState = useCallback(
        (gamesIds: GameId[]): void => {
            if (!topOptionsRef.current) {
                bettingOptionsService.fetchTopOptions().then((options) => {
                    topOptionsRef.current = options;
                    selectedGames.store.addRandom(gamesIds, games, options);
                });
            } else {
                selectedGames.store.addRandom(gamesIds, games, topOptionsRef.current);
            }
        },
        [games],
    );

    return <Context.Provider value={dispatchNewState}>{props.children}</Context.Provider>;
};

export const useRandomFunction = (): IRandomFunction => {
    const context = useContext(Context);

    if (context === undefined) {
        throw new Error('useRandomFunction must be used within a AddRandomFunctionProvider');
    }

    return context;
};
