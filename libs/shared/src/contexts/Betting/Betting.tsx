import { PropsWithChildren, JS<PERSON>, useState, useEffect, useMemo } from 'react';
import {
    createContext,
    useContext as useContextLib,
    useContextSelector,
} from 'use-context-selector';
import { SHOW_NOTIFICATION_TIME } from '@bg-shared/constants/constants';
import { t } from '@bg-shared/business/Translate';
import { roundValue } from '@betgames/bg-tools';
import { playerActivityService } from '@bg-shared/business/PlayerActivity';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { useAmountValue } from '@bg-shared/contexts/Betting/hooks/useAmountValue';
import { useToggleBetslip } from '@bg-shared/contexts/Betting/hooks/useToggleBetslip';
import { useAlert } from '@bg-shared/contexts/Betting/hooks/useAlert';
import { webAPIIntegration, WebIntegrationType } from '@bg-shared/integrations/WebIntegration';
import { bettingService } from '@bg-shared/business/Betting';
import { selectedGames } from '@bg-shared/business/SelectedGames';
import { parseErrorText } from '@bg-shared/utils/parseErrorText';
import { useGetSelectedOptions } from '@bg-shared/business/SelectedGames/hooks/useGetSelectedOptions';
import { NotificationType } from '@bg-shared/enums/NotificationType';
import { IPlaceBet } from '@bg-shared/contexts/Betting/interfaces/IBetting';
import { IBasicWebIntegration } from '@bg-shared/integrations/WebIntegration/interfaces';
import { iframeIntegration } from '@bg-shared/integrations';
import { IBettingContext, IBettingState } from './interfaces';

const Context = createContext<IBettingState>(null);

Context.displayName = 'BettingContext';

const useStateInternal = (): IBettingState => {
    const context = useContextLib(Context);

    if (context === undefined) {
        throw new Error('useContext must be used within a BetslipBettingView.Provider');
    }

    return context;
};

const useSelector = <T,>(selector: (value: IBettingState) => T): T =>
    useContextSelector<IBettingState, T>(Context, selector);

const Provider = (props: PropsWithChildren): JSX.Element => {
    const { partnerSettings } = partnerSettingsStore;
    const [runsCount, setRunsCount] = useState<number>(1);
    const [fromBottom, setFromBottom] = useState<boolean>(false);
    const [processing, setProcessing] = useState<boolean>(false);
    const selectedBettingOptions = useGetSelectedOptions();
    const { amountValue, setAmountValue } = useAmountValue();
    const [open, setOpen, closeDelayed] = useToggleBetslip(selectedBettingOptions.length);
    const [alert, setAlert] = useAlert();
    const totalOddValue = roundValue(
        selectedBettingOptions.reduce(
            (final, odd) => final * odd.value,
            selectedBettingOptions.length ? 1 : 0,
        ),
        2,
    );
    const isBatchedOptions = selectedBettingOptions[0]?.isBatchItem;
    let totalAmountValue = amountValue * runsCount;
    if (isBatchedOptions) {
        totalAmountValue *= selectedBettingOptions.length;
    }
    const possibleWinValue = totalAmountValue * totalOddValue;

    const handleSuccess = () => {
        setAlert({
            type: NotificationType.SUCCESS,
            message: t.string('bet_success'),
            timeout: SHOW_NOTIFICATION_TIME,
        });

        setProcessing(false);
        setOpen(false, false);
    };

    useEffect(() => {
        if (selectedBettingOptions.length) {
            setRunsCount(1);
            setAlert(null);
        }
    }, [selectedBettingOptions.map((bettingOption) => bettingOption.gameId).join('_')]);

    const placeBet: IPlaceBet = async (params) => {
        iframeIntegration.realityCheck();

        playerActivityService.reset();

        setAlert(null);

        if (!amountValue) {
            setAlert({
                type: NotificationType.WARNING,
                message: t.error('amount_should_be_positive'),
                timeout: SHOW_NOTIFICATION_TIME,
            });

            return;
        }

        setProcessing(true);

        try {
            await bettingService.placeBet({
                amountValue,
                runsCount,
                totalOddValue,
                odds: params.odds || selectedBettingOptions,
                isBatch: params.isBatch,
                gameId: params.gameId,
            });
        } catch (e) {
            const error = e as Error & { status?: number };
            const message = parseErrorText(error);

            setAlert({
                message,
                type: NotificationType.WARNING,
                timeout: SHOW_NOTIFICATION_TIME,
            });
            setProcessing(false);
            closeDelayed();

            webAPIIntegration<IBasicWebIntegration>(
                partnerSettingsStore.webIntegrationType,
                (api) => {
                    if (
                        // place bet error overlaps with the WS errors for BGI integration
                        // TODO - check if this is okay as long-term solution
                        partnerSettingsStore.webIntegrationType === WebIntegrationType.BGI &&
                        error.status === 400
                    ) {
                        return;
                    }
                    api.errorMessage(message);
                },
            );

            return;
        }

        selectedGames.store.reset(params.isBatch);
        setRunsCount(1);

        if (!partnerSettings.rememberLastStake) {
            setAmountValue(0);
        }

        handleSuccess();
        iframeIntegration.balanceCheck();
    };

    const cachedValue = useMemo(
        () => ({
            open,
            setOpen,

            runsCount,
            setRunsCount,

            fromBottom,
            setFromBottom,

            amountValue,
            setAmountValue,

            placeBet,

            alert,
            processing,
            totalOddValue,
            totalAmountValue,
            possibleWinValue,
        }),
        [
            open,
            runsCount,
            fromBottom,
            amountValue,
            alert,
            processing,
            totalOddValue,
            totalAmountValue,
            possibleWinValue,
        ],
    );

    // TODO: update react and types
    // eslint-disable-next-line
    // @ts-ignore
    return <Context.Provider value={cachedValue}>{props.children}</Context.Provider>;
};

export const Betting: IBettingContext = {
    Context,
    Provider,
    useState: useStateInternal,
    useAmountValue: () => useSelector((state) => state.amountValue),
    useSetAmountValue: () => useSelector((state) => state.setAmountValue),
    useOpenBetslip: () => useSelector((state) => state.open),
    useSetOpenBetslip: () => useSelector((state) => state.setOpen),
    useProcessing: () => useSelector((state) => state.processing),
    useAlert: () => useSelector((state) => state.alert),
    useShowSuccessAlert: () =>
        useSelector((state) => state.alert?.type === NotificationType.SUCCESS),
    useFromBottom: () => useSelector((state) => state.fromBottom),
    useRunsCount: () => useSelector((state) => [state.runsCount, state.setRunsCount]),
};
