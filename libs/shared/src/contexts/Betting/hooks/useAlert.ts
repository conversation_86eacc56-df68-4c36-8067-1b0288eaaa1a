import { useState, useRef, useCallback } from 'react';
import { IAlert } from '@bg-shared/contexts/Betting/interfaces';
import { useUnmount } from '@betgames/bg-tools';

interface IAlertParams extends IAlert {
    timeout?: number;
}

export const useAlert = (): [IAlert | null, (alert: IAlertParams) => void] => {
    const [alert, setAlert] = useState<IAlert>(null);
    const timeout = useRef<number>(null);

    const setNewAlert = useCallback((params: IAlertParams | null) => {
        if (!params) {
            setAlert(null);
            return;
        }

        setAlert({
            type: params.type,
            message: params.message,
        });

        if (params.timeout) {
            if (timeout.current) {
                clearTimeout(timeout.current);
            }
            timeout.current = setTimeout(() => {
                setAlert(null);
                timeout.current = null;
            }, params.timeout);
        }
    }, []);

    useUnmount(() => {
        clearTimeout(timeout.current);
    });

    return [alert, setNewAlert];
};
