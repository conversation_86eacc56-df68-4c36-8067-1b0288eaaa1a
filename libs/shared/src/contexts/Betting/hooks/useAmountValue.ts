import { useState, useCallback, useEffect } from 'react';
import { storageRepository } from '@betgames/bg-tools';
import { webAPIIntegration } from '@bg-shared/integrations/WebIntegration/webAPIIntegration';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { IBGIWebIntegration } from '@bg-shared/integrations/WebIntegration/interfaces';

const STORAGE_KEY = 'amount';
const storageAmount = Number(storageRepository.getItem(STORAGE_KEY)) || 0;

interface IState {
    amountValue: number;
    setAmountValue: ICallback<number>;
}

export const useAmountValue = (): IState => {
    const [amount, setAmount] = useState<number>(storageAmount);

    const updateWebAPIAmount = (value: number) => {
        webAPIIntegration<IBGIWebIntegration>(partnerSettingsStore.webIntegrationType, (api) => {
            api.stakeUpdate(value);
        });
    };

    const setAmountValue = useCallback((value: number) => {
        storageRepository.setItem(STORAGE_KEY, value.toString());
        setAmount(value);
        updateWebAPIAmount(value);
    }, []);

    useEffect(() => {
        updateWebAPIAmount(amount);
    }, []);

    return {
        amountValue: amount,
        setAmountValue,
    };
};
