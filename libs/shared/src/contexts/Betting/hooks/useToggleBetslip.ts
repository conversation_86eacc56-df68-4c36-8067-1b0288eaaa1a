import { useState, useRef, useCallback, useEffect } from 'react';
import { useUnmount } from '@betgames/bg-tools';
import { HOLD_BETSLIP_TIME } from '@bg-shared/constants/constants';
import { useMobileView } from '@bg-shared/hooks/useMobileView';
import { iframeIntegration } from '@bg-shared/integrations';

let lastScrollY: number = null;

export const useToggleBetslip = (
    optionsCount: number,
): [boolean, (flag: boolean, fromBottom: boolean) => void, () => void] => {
    const isMobileView = useMobileView();
    const [opened, toggle] = useState<boolean>(false);
    const timeoutIdRef = useRef<number>();

    const toggleBetslip = useCallback(
        (flag: boolean, fromBottom = false) => {
            if (isMobileView) {
                if (flag) {
                    iframeIntegration.getPartnerScrollYOffset().then((scrollY) => {
                        lastScrollY = scrollY;
                    });

                    if (!fromBottom) {
                        iframeIntegration.scrollToRelative();
                    }
                } else if (opened && lastScrollY !== null) {
                    iframeIntegration.scrollToAbsolute(lastScrollY);
                }

                if (!flag) {
                    clearTimeout(timeoutIdRef.current);
                }
            }

            toggle(flag);
        },
        [isMobileView, opened],
    );

    const closeDelayed = useCallback(() => {
        timeoutIdRef.current = setTimeout(() => {
            toggle(false);
        }, HOLD_BETSLIP_TIME);
    }, []);

    useEffect(() => {
        if (optionsCount) {
            clearTimeout(timeoutIdRef.current);
        }
    }, [optionsCount]);

    useUnmount(() => {
        clearTimeout(timeoutIdRef.current);
    });

    return [opened, toggleBetslip, closeDelayed];
};
