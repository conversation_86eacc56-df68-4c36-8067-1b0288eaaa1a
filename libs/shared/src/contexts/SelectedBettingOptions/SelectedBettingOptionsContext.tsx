import { PropsWithChildren, JSX, useState, useEffect } from 'react';
import { useStore } from '@betgames/bg-state-manager';
import { createContext, useContext } from 'use-context-selector';
import { GamesContexts } from '@bg-shared/contexts/Games';
import { ISelectedBettingOption } from '@bg-shared/interfaces/IBettingOption';
import { InitialPageSkeleton } from '@bg-components/Skeleton/InitialPageSkeleton'; // TODO - reference to another library
import { selectedGames, selectedGamesSelectors } from '@bg-shared/business/SelectedGames';
import { ISelectedGameModel } from '@bg-shared/business/SelectedGames/interfaces';
import { formatSimpleOption } from '@bg-shared/business/SelectedGames/utils/formatSimpleOption';

interface IProps {
    resolved?: boolean;
}

const Context = createContext<ISelectedBettingOption[]>(null);

export const Provider = (props: PropsWithChildren<IProps>): JSX.Element => {
    const games = GamesContexts.useGamesDataForBetslip();
    const selected = useStore(selectedGames.store, selectedGamesSelectors.getNotBatchedOptions);
    const [loaded, setLoaded] = useState<boolean>(!!props.resolved);

    const resolveInitial = (options: ISelectedGameModel[]): void => {
        if (!options || !options.length) {
            return;
        }

        const filteredOptions = options.filter(
            (option) => !!games[option.gameId]?.data?.bettingOptions?.[option.optionId],
        );

        selectedGames.store.update(filteredOptions);
    };

    useEffect(() => {
        resolveInitial(selected);

        if (!props.resolved) {
            setLoaded(true);
        }
    }, []);

    if (!loaded) {
        return <InitialPageSkeleton />;
    }

    const formattedOptions = selected.map((option) =>
        formatSimpleOption(games[option.gameId], option),
    );

    // TODO: update react and types
    // eslint-disable-next-line
    // @ts-ignore
    return <Context.Provider value={formattedOptions}>{props.children}</Context.Provider>;
};

export const useStateInternal = (): ISelectedBettingOption[] =>
    useContext<ISelectedBettingOption[]>(Context);

export const SelectedBettingOptionsContext = {
    Provider,
    useState: useStateInternal,
};
