import {
    PropsWithChildren,
    JSX,
    createContext as createContextReact,
    useMemo,
    useContext as useContextReact,
} from 'react';
import {
    createContext,
    useContextSelector,
    useContext,
    Context as IContext,
} from 'use-context-selector';
import { IDispatch } from '@bg-shared/interfaces/context';

export interface IContainer<State, Props = Record<string, number | Element>> {
    Context: IContext<State>;
    Provider: (props: PropsWithChildren<Props>) => JSX.Element;
    useDispatch: () => IDispatch<State>;
    useSelector: <T>(selector: (value: State) => T) => T;
    useState: (param?: number | string) => State;
}

export const createContainer = <State, Props = Record<string, number> | PropsWithChildren>(
    useValue: (props: Props) => [State, IDispatch<State>],
    displayName?: string, // for react-devtools
): IContainer<State, Props> => {
    const Context = createContext<State>(null);
    const DispatchContext = createContextReact<IDispatch<State>>(undefined);

    if (displayName) {
        Context.displayName = displayName;
        DispatchContext.displayName = `Dispatch:${displayName}`;
    }

    const Provider = (props: PropsWithChildren<Props>): JSX.Element => {
        const [value, dispatch] = useValue(props);
        const contextValue = useMemo(() => value, [value]);
        const dispatchValue = useMemo(() => dispatch, [dispatch]);

        return (
            <DispatchContext.Provider value={dispatchValue}>
                {/* TODO: update react and types */}
                {/* eslint-disable-next-line */}
                {/* @ts-ignore */}
                <Context.Provider value={contextValue}>{props.children}</Context.Provider>
            </DispatchContext.Provider>
        );
    };

    const useDispatch = (): IDispatch<State> => {
        const context = useContextReact(DispatchContext);

        if (context === undefined) {
            throw new Error(
                `useDispatch must be used within a ${
                    DispatchContext.displayName ?? 'Container'
                }.Provider`,
            );
        }

        return context;
    };

    const useState = (): State => useContext<State>(Context);

    const useSelector = <T,>(selector: (value: State) => T): T =>
        useContextSelector<State, T>(Context, selector);

    return {
        Context,
        Provider,
        useDispatch,
        useSelector,
        useState,
    };
};
