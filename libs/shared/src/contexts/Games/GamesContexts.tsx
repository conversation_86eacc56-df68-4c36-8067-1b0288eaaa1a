import { createContext, PropsWithChildren, JSX, useState, useEffect, useContext } from 'react';
import { forceCast, taxes } from '@betgames/bg-tools';
import { IBetslipGameId, ILotteryGameId } from '@bg-shared/interfaces/GameId';
import { GameId } from '@bg-shared/enums/GameId';
import { IBetslipGameState } from '@bg-shared/contexts/Games/interfaces/IBetslipGames';
import { gamesService } from '@bg-shared/business/Games';
import { assetsService } from '@bg-shared/business/Assets';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { gamesOptionsApi } from '@bg-shared/business/GamesOptions';
import { InitialPageSkeleton } from '@bg-components/Skeleton/InitialPageSkeleton';
import { useErrorHandling } from '@bg-components/ErrorBoundary/ErrorBoundaryContext';
import { ClassicGameDefinition } from '@bg-shared/utils';
import { IGenericDraw } from '@bg-shared/interfaces';
import { sessionStore } from '@bg-shared/business/Session';
import { ILotteryItem } from '@bg-shared/interfaces/ILotteryItem';
import { ILiveLotteryGameState } from '@bg-shared/contexts/Games/LiveLottery/interfaces';
import { IBetslipGames, IGamesContainers } from './interfaces';
import { createGamesContexts } from './createGamesContexts';
import { ComposeComponents } from './ComposeComponents';

export const Context = createContext<IGamesContainers>(undefined);

const Provider = (props: PropsWithChildren): JSX.Element => {
    const triggerError = useErrorHandling();
    const [container, setContainer] = useState<{
        Contexts: IGamesContainers;
        Providers: ((props: PropsWithChildren) => JSX.Element)[];
    }>({ Contexts: null, Providers: [] });

    useEffect(() => {
        const { availableGamesIds } = partnerSettingsStore;

        if (availableGamesIds?.length) {
            const { gamesPresetIds, availableInHouseGamesIds, availablePLSGamesIds } =
                partnerSettingsStore;

            const inHouseGames = availableInHouseGamesIds.map((gameId) => ({
                gameId,
                presetId: gamesPresetIds[gameId],
            }));

            Promise.all([
                gamesOptionsApi.getOptionsConfigs(
                    availableInHouseGamesIds,
                    sessionStore.partnerCode,
                ),
                gamesService.getGamesData(inHouseGames, taxes.schemeId),
                gamesService.getPLSGamesData(availablePLSGamesIds),
            ])
                .then(([bettingOptions, gamesData, plsGameData]) => {
                    // Todo: remove when real data will come
                    availableGamesIds
                        .filter((gameId) => ClassicGameDefinition.isHZGame(gameId))
                        .forEach((gameId) => {
                            if (!bettingOptions[gameId]) {
                                bettingOptions[gameId] = {
                                    data: undefined,
                                    preset: 80,
                                    items: [],
                                    groups: [{ id: 1, options: [] }],
                                };
                            }

                            if (!gamesData[gameId]) {
                                gamesData[gameId] = {} as IGenericDraw;
                            }
                        });

                    if (
                        availableGamesIds.includes(GameId.RGS_ROULETTE_EURO) &&
                        !gamesData[GameId.RGS_ROULETTE_EURO]
                    ) {
                        gamesData[GameId.RGS_ROULETTE_EURO] = {} as IGenericDraw;
                    }

                    if (
                        availableGamesIds.includes(GameId.LUCKY_LENNY) &&
                        !gamesData[GameId.LUCKY_LENNY]
                    ) {
                        gamesData[GameId.LUCKY_LENNY] = {} as IGenericDraw;
                    }
                    // Todo - End

                    partnerSettingsStore.filterAvailableGamesIds(
                        Object.keys(gamesData).map(Number),
                    );

                    setContainer(createGamesContexts(gamesData, bettingOptions, plsGameData));

                    assetsService.setupLoader();
                })
                .catch((e) => {
                    triggerError(
                        new Error(e instanceof Error ? e.message : 'error in application setup'),
                    );
                });
        }
    }, []);

    if (!container?.Contexts) {
        if (_featuresConfig.brandedConfig?.logos) {
            return null;
        }

        return <InitialPageSkeleton />;
    }

    return (
        <ComposeComponents components={container.Providers}>
            <Context.Provider value={container.Contexts}>{props.children}</Context.Provider>
        </ComposeComponents>
    );
};

const useContexts = (): IGamesContainers => {
    const context = useContext(Context);

    if (context === undefined) {
        throw new Error('useContainers must be used within a GamesContainers.Provider');
    }

    return context;
};

export const GamesContexts = {
    Provider,
    useContexts,
    useLotteryItems: (gameId: GameId): ILotteryItem[] => {
        const game = useContexts()[gameId as ILotteryGameId].useState();
        return (game as ILiveLotteryGameState).gameItems;
    },
    useGamesDataForBetslip: (): Record<GameId, IBetslipGameState> => {
        const Containers = useContexts();

        return forceCast<IBetslipGameId[]>(Object.keys(Containers)).reduce((result, gameId) => {
            result[gameId] = Containers[gameId].useState();
            return result;
        }, {} as IBetslipGames);
    },
};
