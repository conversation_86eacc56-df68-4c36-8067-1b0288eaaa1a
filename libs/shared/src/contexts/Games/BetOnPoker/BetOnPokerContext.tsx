import { useMemo, useRef, useEffect } from 'react';
import { createContainer } from '@bg-shared/contexts/createContainer';
import { taxes, useImmutableState } from '@betgames/bg-tools';
import { webSocketsRepository } from '@bg-shared/infrastructure/WebSockets';
import { GameId } from '@bg-shared/enums/GameId';
import { GAME_INFO_CHANNELS } from '@bg-shared/constants/socketChannels';
import { HOLD_DRAW_UPDATE_TIME } from '@bg-shared/constants/constants';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { sessionStore } from '@bg-shared/business/Session';
import { gamesStatusService } from '@bg-shared/business/GamesStatus';
import { balanceSelectors, balanceStore } from '@bg-shared/business/Balance';
import { IPokerDraw } from '@bg-shared/interfaces';
import { isGameActive } from '@bg-shared/utils/isGameActive';
import { webAPIIntegration } from '@bg-shared/integrations';
import { gamesRunsEntity } from '@bg-shared/business/GamesRuns';
import { IBetOnPokerContext, IBetOnPokerGameState, IBetOnPokerProps } from './interfaces';
import { mapPropsToState } from './mapPropsToState';
import { mutations } from './mutations';

export const BetOnPokerContext: IBetOnPokerContext = createContainer<
    IBetOnPokerGameState,
    IBetOnPokerProps
>((props) => {
    const webSockets = webSocketsRepository;
    const gameSettings = partnerSettingsStore.getEnabledGame(GameId.POKER);
    const [state, dispatch] = useImmutableState<IBetOnPokerGameState>(
        useMemo(() => mapPropsToState(props, sessionStore.oddsFormat), []),
    );

    const stateRunIdRef = useRef<number>(state.data.runId);
    stateRunIdRef.current = state.data.runId;

    useEffect(() => {
        gamesStatusService.configureTimer({
            gameId: GameId.POKER,
            seconds: props.draw.run.secondsLeft,
            timestamp: props.draw.infoTime,
        });

        webSockets.on<IPokerDraw>(
            `${GAME_INFO_CHANNELS[GameId.POKER]}:${gameSettings.presetId}:${taxes.schemeId}`,
            (data) => {
                const isNewRun = !!data.prevRun.results;

                gamesRunsEntity.store(GameId.POKER).update({
                    run: {
                        id: data.run.id,
                        code: data.run.code,
                    },
                    round: {
                        id: data.run.runRoundId,
                        number: data.run.roundId,
                    },
                });

                setTimeout(() => {
                    if (isNewRun) {
                        dispatch((draft) => {
                            mutations.setResults(draft, data);
                        });
                    }

                    setTimeout(
                        () => {
                            if (isGameActive(GameId.POKER) && isNewRun) {
                                const params = {
                                    balance: balanceSelectors.getBalance(GameId.POKER)(
                                        balanceStore.value,
                                    ),
                                    prevRunId: gamesRunsEntity.store(GameId.POKER).value?.run.id,
                                };

                                webAPIIntegration(
                                    partnerSettingsStore.webIntegrationType,
                                    (api) => {
                                        api.runFinished(params);
                                    },
                                );
                            }

                            gamesStatusService.updateTimer({
                                gameId: GameId.POKER,
                                seconds: data.run.secondsLeft,
                                timestamp: data.infoTime,
                            });

                            dispatch((draft) => {
                                mutations.updateState(draft, data, sessionStore.oddsFormat);
                            });
                        },
                        isNewRun ? HOLD_DRAW_UPDATE_TIME[GameId.POKER] : 0,
                    );
                }, gameSettings.streamDelay);
            },
        );
    }, []);

    return [state, dispatch];
}, 'BetOnPokerContext');
