import { GameId } from '@bg-shared/enums/GameId';
import { BettingOptionsFormat } from '@betgames/bg-tools';
import { BettingOptionStatus } from '@bg-shared/enums/BettingOptionStatus';
import { IPokerDraw } from '@bg-shared/interfaces/Draws/Poker';
import { mapWebSocketsBettingOptions } from '../utils/mapWebSocketsBettingOptions';
import { assignCardsToBettingOptions } from './utils/assignCardsToBettingOptions';
import { IBetOnPokerDraftState } from './interfaces';

export const mutations = {
    updateState: (
        draft: IBetOnPokerDraftState,
        data: IPokerDraw,
        oddsFormat: BettingOptionsFormat,
    ): void => {
        draft.data.results = null;
        draft.data.cards = data.run.cards;
        draft.data.drawCode = data.run.code;
        draft.data.runId = data.run.id;
        draft.data.roundId = data.run.roundId;
        draft.data.runRoundId = data.run.runRoundId;

        const bettingOptions = mapWebSocketsBettingOptions({
            options: data.odds,
            gameId: GameId.POKER,
            format: oddsFormat,
        });

        assignCardsToBettingOptions(data.run.cards, draft.oddGroups[0].ids, bettingOptions);

        Object.keys(draft.data.bettingOptions).forEach((idString) => {
            const id = Number(idString);
            Object.assign(draft.data.bettingOptions[id], bettingOptions[id]);
        });
    },

    setResults: (draft: IBetOnPokerDraftState, data: IPokerDraw): void => {
        draft.data.results = data.prevRun.results;
        draft.data.cards = data.prevRun.lastCards;

        Object.values(draft.data.bettingOptions).forEach((option) => {
            option.status = data.prevRun.wonOdds.includes(option.id.toString())
                ? BettingOptionStatus.WON
                : BettingOptionStatus.LOST;
        });
    },
};
