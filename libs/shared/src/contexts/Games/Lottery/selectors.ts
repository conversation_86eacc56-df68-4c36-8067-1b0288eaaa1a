import { IMultiplierTable } from '@bg-shared/interfaces/IGameOptionsGroup';
import { ILotteryGameState, ILotterySelectData } from './interfaces';

export const lotterySelectors = {
    selectData: (state: ILotteryGameState): ILotterySelectData => ({
        ...state.data,
        groups: state.groups,
        gameItems: state.gameItems,
        oddsGroups: state.oddGroups,
    }),

    getMultipliers:
        (optionId: number) =>
        (state: ILotteryGameState): IMultiplierTable[] =>
            state.groups
                .reduce((acc, group) => {
                    acc.push(...group.options);
                    return acc;
                }, [])
                .find((option) => option.id === optionId)?.multipliers,

    getMultipliersNumbers:
        (optionId: number) =>
        (state: ILotteryGameState): number[] =>
            lotterySelectors
                .getMultipliers(optionId)(state)
                .map((item) => item.multiplier),
};
