import { useMemo, useRef, useEffect } from 'react';
import { ILotteryGameId } from '@bg-shared/interfaces/GameId';
import { createContainer } from '@bg-shared/contexts/createContainer';
import { ILotteryDraw } from '@bg-shared/interfaces/Draws/ILotteryDraw';
import { webSocketsRepository } from '@bg-shared/infrastructure/WebSockets';
import { sessionStore } from '@bg-shared/business/Session';
import { useImmutableState } from '@betgames/bg-tools';
import { GAME_INFO_CHANNELS } from '@bg-shared/constants/socketChannels';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { GameId } from '@bg-shared/enums/GameId';
import { GAME_STOP_BETTING_OFFSET, HOLD_DRAW_UPDATE_TIME } from '@bg-shared/constants/constants';
import { gamesStatusService } from '@bg-shared/business/GamesStatus';
import { balanceSelectors, balanceStore } from '@bg-shared/business/Balance';
import { gamesRunsEntity } from '@bg-shared/business/GamesRuns';
import { gamesOptionsEntity } from '@bg-shared/business/GamesOptions';
import { keyBy } from 'lodash-unified';
import { isGameActive } from '@bg-shared/utils/isGameActive';
import { webAPIIntegration } from '@bg-shared/integrations';
import { mapToGroupsIds } from '@bg-shared/business/GamesOptions/utils/mapToGroupsIds';
import { ILotteryContext, ILotteryGameState, ILotteryProps } from './interfaces';
import { mapPropsToState } from './mapPropsToState';
import { mutations } from './mutations';

export const createLotteryContext = (gameId: ILotteryGameId): ILotteryContext =>
    createContainer<ILotteryGameState, ILotteryProps>((props) => {
        const webSockets = webSocketsRepository;
        const delay = partnerSettingsStore.getGameDelay(gameId);
        const [state, dispatch] = useImmutableState<ILotteryGameState>(
            useMemo(() => mapPropsToState(props, gameId, sessionStore.oddsFormat), []),
        );

        const stateRunIdRef = useRef<number>(state.data.runId);
        stateRunIdRef.current = state.data.runId;

        useEffect(() => {
            gamesRunsEntity.store(gameId).update({
                run: {
                    id: state.data.runId,
                    code: state.data.drawCode,
                },
                nextRun: {
                    id: state.data.nextRunId,
                    code: state.data.nextDrawCode,
                },
            });

            gamesOptionsEntity.store(gameId).update({
                options: keyBy(
                    props.bettingOptions.groups.map((group) => group.options).flat(),
                    'id',
                ),
                groups: mapToGroupsIds(props.bettingOptions.groups),
                items: props.bettingOptions.items,
            });

            gamesStatusService.configureTimer({
                gameId,
                seconds: props.draw.secondsLeft,
                timestamp: props.draw.infoTime,
                offsetSeconds: GAME_STOP_BETTING_OFFSET,
            });

            webSockets.on<ILotteryDraw>(GAME_INFO_CHANNELS[gameId], (data) => {
                const totalDelay =
                    delay +
                    ([GameId.RNG_FOOTBALL_GRID, GameId.SHOVA52, GameId.LUCKY_KICKS].includes(
                        data.lotteryEventId,
                    )
                        ? HOLD_DRAW_UPDATE_TIME[data.lotteryEventId]
                        : 0);

                setTimeout(() => {
                    if (isGameActive(gameId)) {
                        const params = {
                            balance: balanceSelectors.getBalance(gameId)(balanceStore.value),
                            prevRunId: gamesRunsEntity.store(gameId).value.run.id,
                        };

                        webAPIIntegration(partnerSettingsStore.webIntegrationType, (api) => {
                            api.runFinished(params);
                        });
                    }

                    dispatch((draft) => {
                        mutations.updateState(draft, data);
                    });

                    gamesRunsEntity.store(gameId).update({
                        run: {
                            id: data.runId,
                            code: data.drawCode,
                        },
                        nextRun: {
                            id: data.nextRunId,
                            code: data.nextDrawCode,
                        },
                    });

                    gamesStatusService.updateTimer({
                        gameId,
                        seconds: data.secondsLeft,
                        timestamp: data.infoTime,
                    });
                }, totalDelay);
            });
        }, []);

        return [state, dispatch];
    }, `LotteryContext:${gameId}`);
