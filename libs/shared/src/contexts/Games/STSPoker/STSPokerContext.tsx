import { useMemo, useRef, useEffect } from 'react';
import { createContainer } from '@bg-shared/contexts/createContainer';
import { taxes, useImmutableState } from '@betgames/bg-tools';
import { webSocketsRepository } from '@bg-shared/infrastructure/WebSockets';
import { sessionStore } from '@bg-shared/business/Session';
import { ISTSPokerDraw } from '@bg-shared/interfaces/Draws/Poker';
import { GAME_INFO_CHANNELS } from '@bg-shared/constants/socketChannels';
import { GameId } from '@bg-shared/enums/GameId';
import { HOLD_DRAW_UPDATE_TIME } from '@bg-shared/constants/constants';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { gamesStatusService } from '@bg-shared/business/GamesStatus';
import { balanceSelectors, balanceStore } from '@bg-shared/business/Balance';
import { isGameActive } from '@bg-shared/utils/isGameActive';
import { webAPIIntegration } from '@bg-shared/integrations';
import { ISTSPokerContextProps, ISTSPokerContext, ISTSPokerGameState } from './interfaces';
import { mapPropsToState } from './mapPropsToState';
import { mutations } from './mutations';

export const STSPokerContext: ISTSPokerContext = createContainer<
    ISTSPokerGameState,
    ISTSPokerContextProps
>((props) => {
    const webSockets = webSocketsRepository;
    const gameSettings = partnerSettingsStore.getEnabledGame(GameId.STS_POKER);
    const [state, dispatch] = useImmutableState<ISTSPokerGameState>(
        useMemo(() => mapPropsToState(props, sessionStore.oddsFormat), []),
    );

    const stateRunIdRef = useRef<number>(state.data.runId);
    stateRunIdRef.current = state.data.runId;

    useEffect(() => {
        gamesStatusService.configureTimer({
            gameId: GameId.STS_POKER,
            seconds: props.draw.run.secondsLeft,
            timestamp: props.draw.infoTime,
        });

        webSockets.on<ISTSPokerDraw>(
            `${GAME_INFO_CHANNELS[GameId.STS_POKER]}:${gameSettings.presetId}:${taxes.schemeId}`,
            (data) => {
                const isNewRun = !!data.prevRun.results;

                setTimeout(() => {
                    if (data.prevRun?.results) {
                        dispatch((draft) => {
                            mutations.setResults(draft, data);
                        });
                    }

                    setTimeout(
                        () => {
                            if (isGameActive(GameId.STS_POKER) && isNewRun) {
                                const params = {
                                    balance: balanceSelectors.getBalance(GameId.STS_POKER)(
                                        balanceStore.value,
                                    ),
                                    prevRunId: stateRunIdRef.current,
                                };

                                webAPIIntegration(
                                    partnerSettingsStore.webIntegrationType,
                                    (api) => {
                                        api.runFinished(params);
                                    },
                                );
                            }

                            gamesStatusService.updateTimer({
                                gameId: GameId.STS_POKER,
                                seconds: data.run.secondsLeft,
                                timestamp: data.infoTime,
                            });

                            dispatch((draft) => {
                                mutations.updateState(draft, data, sessionStore.oddsFormat);
                            });
                        },
                        isNewRun ? HOLD_DRAW_UPDATE_TIME[GameId.STS_POKER] : 0,
                    );
                }, gameSettings.streamDelay);
            },
        );
    }, []);

    return [state, dispatch];
}, 'STSPokerContext');
