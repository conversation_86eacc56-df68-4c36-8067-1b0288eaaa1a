import { PropsWithChildren, JS<PERSON> } from 'react';
import { forceCast } from '@betgames/bg-tools';
import { GameId } from '@bg-shared/enums/GameId';
import { IGenericDraw } from '@bg-shared/interfaces/Draws/IGenericDraw';
import { createLotteryContext } from '@bg-shared/contexts/Games/Lottery';
import { createLiveLotteryContext } from '@bg-shared/contexts/Games/LiveLottery';
import { ILotteryDraw } from '@bg-shared/interfaces/Draws/ILotteryDraw';
import { BetOnPokerContext } from '@bg-shared/contexts/Games/BetOnPoker';
import { IPokerDraw } from '@bg-shared/interfaces/Draws/Poker';
import { SixPlusPokerContext } from '@bg-shared/contexts/Games/SixPlusPoker';
import { ISixPlusPokerDraw } from '@bg-shared/interfaces/Draws/SixPlusPoker';
import { BaccaratContext } from '@bg-shared/contexts/Games/Baccarat';
import { IBaccaratDraw } from '@bg-shared/interfaces/Draws/Baccarat';
import { WarOfBetsContext } from '@bg-shared/contexts/Games/WarOfBets';
import { IWarOfBetsDraw } from '@bg-shared/interfaces/Draws/WarOfBets';
import { ILiveLotteryDraw } from '@bg-shared/interfaces/Draws/ILiveLotteryDraw';
import { IGameBlackjackDraw } from '@bg-shared/interfaces/Draws/IBlackjackDraw';
import { STSPokerContext } from '@bg-shared/contexts/Games/STSPoker';
import { ILegacyOptionsConfig } from '@bg-shared/business/GamesOptions/interfaces';
import {
    gameBlackjackEvents,
    gameBlackjackStore,
    gamesRouletteStoresEntity,
    gameRouletteEvents,
} from '@bg-shared/business/Games';
import { IPLSGameId } from '@bg-shared/interfaces';
import { IGamesContainers } from './interfaces';

export const createGamesContexts = (
    gamesData: Partial<Record<GameId, IGenericDraw>>,
    gamesOptions: Partial<Record<GameId, ILegacyOptionsConfig>>,
    plsGamesData: Partial<Record<GameId, IGameBlackjackDraw>>,
): {
    Contexts: IGamesContainers;
    Providers: ((props: PropsWithChildren) => JSX.Element)[];
} => {
    const Contexts = {} as IGamesContainers;
    const Providers: ((props: PropsWithChildren) => JSX.Element)[] = [];

    if (gamesData[GameId.LUCKY7]) {
        const Container = createLotteryContext(GameId.LUCKY7);

        Contexts[GameId.LUCKY7] = Container;

        Providers.push((props) => (
            <Container.Provider
                draw={gamesData[GameId.LUCKY7] as ILotteryDraw}
                bettingOptions={gamesOptions[GameId.LUCKY7]}
            >
                {props.children}
            </Container.Provider>
        ));
    }

    if (gamesData[GameId.LUCKY6]) {
        const Container = createLotteryContext(GameId.LUCKY6);

        Contexts[GameId.LUCKY6] = Container;

        Providers.push((props) => (
            <Container.Provider
                draw={gamesData[GameId.LUCKY6] as ILotteryDraw}
                bettingOptions={gamesOptions[GameId.LUCKY6]}
            >
                {props.children}
            </Container.Provider>
        ));
    }

    if (gamesData[GameId.LUCKY5]) {
        const Container = createLotteryContext(GameId.LUCKY5);

        Contexts[GameId.LUCKY5] = Container;

        Providers.push((props) => (
            <Container.Provider
                draw={gamesData[GameId.LUCKY5] as ILotteryDraw}
                bettingOptions={gamesOptions[GameId.LUCKY5]}
            >
                {props.children}
            </Container.Provider>
        ));
    }

    if (gamesData[GameId.BETWAY_LUCKY7]) {
        const Container = createLotteryContext(GameId.BETWAY_LUCKY7);

        Contexts[GameId.BETWAY_LUCKY7] = Container;

        Providers.push((props) => (
            <Container.Provider
                draw={gamesData[GameId.BETWAY_LUCKY7] as ILotteryDraw}
                bettingOptions={gamesOptions[GameId.BETWAY_LUCKY7]}
            >
                {props.children}
            </Container.Provider>
        ));
    }

    if (gamesData[GameId.DICE_DUEL]) {
        const Container = createLiveLotteryContext(GameId.DICE_DUEL);

        Contexts[GameId.DICE_DUEL] = Container;

        Providers.push((props) => (
            <Container.Provider
                draw={gamesData[GameId.DICE_DUEL] as ILiveLotteryDraw}
                bettingOptions={gamesOptions[GameId.DICE_DUEL]}
            >
                {props.children}
            </Container.Provider>
        ));
    }

    if (gamesData[GameId.WHEEL]) {
        const Container = createLiveLotteryContext(GameId.WHEEL);

        Contexts[GameId.WHEEL] = Container;

        Providers.push((props) => (
            <Container.Provider
                draw={gamesData[GameId.WHEEL] as ILiveLotteryDraw}
                bettingOptions={gamesOptions[GameId.WHEEL]}
            >
                {props.children}
            </Container.Provider>
        ));
    }

    if (gamesData[GameId.RNG_WHEEL]) {
        const Container = createLotteryContext(GameId.RNG_WHEEL);

        Contexts[GameId.RNG_WHEEL] = Container;

        Providers.push((props) => (
            <Container.Provider
                draw={gamesData[GameId.RNG_WHEEL] as ILotteryDraw}
                bettingOptions={gamesOptions[GameId.RNG_WHEEL]}
            >
                {props.children}
            </Container.Provider>
        ));
    }

    if (gamesData[GameId.RNG_FOOTBALL_GRID]) {
        const Container = createLotteryContext(GameId.RNG_FOOTBALL_GRID);

        Contexts[GameId.RNG_FOOTBALL_GRID] = Container;

        Providers.push((props) => (
            <Container.Provider
                draw={gamesData[GameId.RNG_FOOTBALL_GRID] as ILotteryDraw}
                bettingOptions={gamesOptions[GameId.RNG_FOOTBALL_GRID]}
            >
                {props.children}
            </Container.Provider>
        ));
    }

    if (gamesData[GameId.RNG_LUCKY7]) {
        const Container = createLotteryContext(GameId.RNG_LUCKY7);

        Contexts[GameId.RNG_LUCKY7] = Container;

        Providers.push((props) => (
            <Container.Provider
                draw={gamesData[GameId.RNG_LUCKY7] as ILotteryDraw}
                bettingOptions={gamesOptions[GameId.RNG_LUCKY7]}
            >
                {props.children}
            </Container.Provider>
        ));
    }

    if (gamesData[GameId.POKER]) {
        Contexts[GameId.POKER] = BetOnPokerContext;

        Providers.push((props) => (
            <BetOnPokerContext.Provider
                draw={gamesData[GameId.POKER] as IPokerDraw}
                bettingOptions={gamesOptions[GameId.POKER]}
            >
                {props.children}
            </BetOnPokerContext.Provider>
        ));
    }

    if (gamesData[GameId.SIX_PLUS_POKER]) {
        Contexts[GameId.SIX_PLUS_POKER] = SixPlusPokerContext;

        Providers.push((props) => (
            <SixPlusPokerContext.Provider
                draw={gamesData[GameId.SIX_PLUS_POKER] as ISixPlusPokerDraw}
                bettingOptions={gamesOptions[GameId.SIX_PLUS_POKER]}
            >
                {props.children}
            </SixPlusPokerContext.Provider>
        ));
    }

    if (gamesData[GameId.BACCARAT]) {
        Contexts[GameId.BACCARAT] = BaccaratContext;

        Providers.push((props) => (
            <BaccaratContext.Provider
                draw={gamesData[GameId.BACCARAT] as IBaccaratDraw}
                bettingOptions={gamesOptions[GameId.BACCARAT]}
            >
                {props.children}
            </BaccaratContext.Provider>
        ));
    }

    if (gamesData[GameId.WAR]) {
        Contexts[GameId.WAR] = WarOfBetsContext;

        Providers.push((props) => (
            <WarOfBetsContext.Provider
                draw={gamesData[GameId.WAR] as IWarOfBetsDraw}
                bettingOptions={gamesOptions[GameId.WAR]}
            >
                {props.children}
            </WarOfBetsContext.Provider>
        ));
    }

    if (gamesData[GameId.STS_POKER]) {
        Contexts[GameId.STS_POKER] = STSPokerContext;

        Providers.push((props) => (
            <STSPokerContext.Provider
                draw={gamesData[GameId.STS_POKER] as IPokerDraw}
                bettingOptions={gamesOptions[GameId.STS_POKER]}
            >
                {props.children}
            </STSPokerContext.Provider>
        ));
    }

    if (gamesData[GameId.RNG_ROULETTE]) {
        gamesRouletteStoresEntity
            .store(GameId.RNG_ROULETTE)
            .init(
                gamesData[GameId.RNG_ROULETTE] as ILotteryDraw,
                gamesOptions[GameId.RNG_ROULETTE],
            );
        Contexts[GameId.RNG_ROULETTE] = forceCast(
            gamesRouletteStoresEntity.store(GameId.RNG_ROULETTE),
        );
        gameRouletteEvents.configure(
            GameId.RNG_ROULETTE,
            gamesRouletteStoresEntity.store(GameId.RNG_ROULETTE),
        );
    }

    // Todo: will be migrated in https://betgamestv.atlassian.net/browse/FTL-5533
    if (gamesData[GameId.SHOVA52]) {
        const Container = createLotteryContext(GameId.SHOVA52);

        Contexts[GameId.SHOVA52] = Container;

        Providers.push((props) => (
            <Container.Provider
                draw={gamesData[GameId.SHOVA52] as ILotteryDraw}
                bettingOptions={gamesOptions[GameId.SHOVA52]}
            >
                {props.children}
            </Container.Provider>
        ));
    }

    if (gamesData[GameId.LUCKY_KICKS]) {
        const Container = createLotteryContext(GameId.LUCKY_KICKS);

        Contexts[GameId.LUCKY_KICKS] = Container;

        Providers.push((props) => (
            <Container.Provider
                draw={gamesData[GameId.LUCKY_KICKS] as ILotteryDraw}
                bettingOptions={gamesOptions[GameId.LUCKY_KICKS]}
            >
                {props.children}
            </Container.Provider>
        ));
    }

    if (gamesData[GameId.ROULETTE_EURO_GE]) {
        gamesRouletteStoresEntity
            .store(GameId.ROULETTE_EURO_GE)
            .init(
                gamesData[GameId.ROULETTE_EURO_GE] as ILotteryDraw,
                gamesOptions[GameId.ROULETTE_EURO_GE],
            );
        Contexts[GameId.ROULETTE_EURO_GE] = forceCast(
            gamesRouletteStoresEntity.store(GameId.ROULETTE_EURO_GE),
        );
        gameRouletteEvents.configure(
            GameId.ROULETTE_EURO_GE,
            gamesRouletteStoresEntity.store(GameId.ROULETTE_EURO_GE),
        );
    }

    if (gamesData[GameId.ROULETTE_EURO_LT]) {
        gamesRouletteStoresEntity
            .store(GameId.ROULETTE_EURO_LT)
            .init(
                gamesData[GameId.ROULETTE_EURO_LT] as ILotteryDraw,
                gamesOptions[GameId.ROULETTE_EURO_LT],
            );
        Contexts[GameId.ROULETTE_EURO_LT] = forceCast(
            gamesRouletteStoresEntity.store(GameId.ROULETTE_EURO_LT),
        );
        gameRouletteEvents.configure(
            GameId.ROULETTE_EURO_LT,
            gamesRouletteStoresEntity.store(GameId.ROULETTE_EURO_LT),
        );
    }

    if (gamesData[GameId.ROULETTE_AMERICAN_GE]) {
        gamesRouletteStoresEntity
            .store(GameId.ROULETTE_AMERICAN_GE)
            .init(
                gamesData[GameId.ROULETTE_AMERICAN_GE] as ILotteryDraw,
                gamesOptions[GameId.ROULETTE_AMERICAN_GE],
            );
        Contexts[GameId.ROULETTE_AMERICAN_GE] = forceCast(
            gamesRouletteStoresEntity.store(GameId.ROULETTE_AMERICAN_GE),
        );
        gameRouletteEvents.configure(
            GameId.ROULETTE_AMERICAN_GE,
            gamesRouletteStoresEntity.store(GameId.ROULETTE_AMERICAN_GE),
        );
    }

    if (gamesData[GameId.ROULETTE_FLASH_LT]) {
        gamesRouletteStoresEntity
            .store(GameId.ROULETTE_FLASH_LT)
            .init(
                gamesData[GameId.ROULETTE_FLASH_LT] as ILotteryDraw,
                gamesOptions[GameId.ROULETTE_FLASH_LT],
            );
        Contexts[GameId.ROULETTE_FLASH_LT] = forceCast(
            gamesRouletteStoresEntity.store(GameId.ROULETTE_FLASH_LT),
        );
        gameRouletteEvents.configure(
            GameId.ROULETTE_FLASH_LT,
            gamesRouletteStoresEntity.store(GameId.ROULETTE_FLASH_LT),
        );
    }

    if (gamesData[GameId.RNG_ROULETTE]) {
        gamesRouletteStoresEntity
            .store(GameId.RNG_ROULETTE)
            .init(
                gamesData[GameId.RNG_ROULETTE] as ILotteryDraw,
                gamesOptions[GameId.RNG_ROULETTE],
            );
        Contexts[GameId.RNG_ROULETTE] = forceCast(
            gamesRouletteStoresEntity.store(GameId.RNG_ROULETTE),
        );
        gameRouletteEvents.configure(
            GameId.RNG_ROULETTE,
            gamesRouletteStoresEntity.store(GameId.RNG_ROULETTE),
        );
    }

    if (plsGamesData) {
        Object.entries(plsGamesData).forEach(([gameId, data]) => {
            const id = Number(gameId) as IPLSGameId;
            gameBlackjackStore.updateGame(id, data);
            gameBlackjackEvents.configure(id);
        });
    }

    return { Contexts, Providers };
};
