import { useMemo, useRef, useEffect } from 'react';
import { createContainer } from '@bg-shared/contexts/createContainer';
import { taxes, useImmutableState } from '@betgames/bg-tools';
import { IBaccaratDraw } from '@bg-shared/interfaces/Draws/Baccarat';
import { webSocketsRepository } from '@bg-shared/infrastructure/WebSockets';
import { GameId } from '@bg-shared/enums/GameId';
import { BACCARAT_SHUFFLE_DECK, GAME_INFO_CHANNELS } from '@bg-shared/constants/socketChannels';
import { HOLD_DRAW_UPDATE_TIME } from '@bg-shared/constants/constants';
import { sessionStore } from '@bg-shared/business/Session';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { gamesStatusService } from '@bg-shared/business/GamesStatus';
import { balanceSelectors, balanceStore } from '@bg-shared/business/Balance';
import { isGameActive } from '@bg-shared/utils/isGameActive';
import { webAPIIntegration } from '@bg-shared/integrations/WebIntegration';
import { IBaccaratContext, IBaccaratGameState, IBaccaratProps } from './interfaces';
import { mapPropsToState } from './mapPropsToState';
import { mutations } from './mutations';

export const BaccaratContext: IBaccaratContext = createContainer<
    IBaccaratGameState,
    IBaccaratProps
>((props) => {
    const webSockets = webSocketsRepository;
    const gameSettings = partnerSettingsStore.getEnabledGame(GameId.BACCARAT);
    const [state, dispatch] = useImmutableState<IBaccaratGameState>(
        useMemo(() => mapPropsToState(props, sessionStore.oddsFormat), []),
    );

    const stateRunIdRef = useRef<number>(state.data.runId);
    stateRunIdRef.current = state.data.runId;

    useEffect(() => {
        gamesStatusService.configureTimer({
            gameId: GameId.BACCARAT,
            seconds: props.draw.run.secondsLeft,
            timestamp: props.draw.infoTime,
        });

        webSockets.on<IBaccaratDraw>(
            `${GAME_INFO_CHANNELS[GameId.BACCARAT]}:${gameSettings.presetId}:${taxes.schemeId}`,
            (data) => {
                const isNewRun = !!data.prevRun.results;

                setTimeout(() => {
                    if (isNewRun) {
                        dispatch((draft) => {
                            mutations.setResults(draft, data);
                        });
                    }

                    setTimeout(
                        () => {
                            if (isGameActive(GameId.BACCARAT) && isNewRun) {
                                const params = {
                                    balance: balanceSelectors.getBalance(GameId.BACCARAT)(
                                        balanceStore.value,
                                    ),
                                    prevRunId: stateRunIdRef.current,
                                };

                                webAPIIntegration(
                                    partnerSettingsStore.webIntegrationType,
                                    (api) => {
                                        api.runFinished(params);
                                    },
                                );
                            }

                            gamesStatusService.updateTimer({
                                gameId: GameId.BACCARAT,
                                seconds: data.run.secondsLeft,
                                timestamp: data.infoTime,
                            });

                            dispatch((draft) => {
                                mutations.updateState(draft, data, sessionStore.oddsFormat);
                            });
                        },
                        isNewRun ? HOLD_DRAW_UPDATE_TIME[GameId.BACCARAT] : 0,
                    );
                }, gameSettings.streamDelay);
            },
        );

        webSockets.on<void>(BACCARAT_SHUFFLE_DECK, () => {
            dispatch((draft) => {
                draft.data.nextDrawShuffleDeck = true;
            });
        });
    }, []);

    return [state, dispatch];
}, 'BaccaratContext');
