import { GameId } from '@bg-shared/enums/GameId';
import { BettingOptionsFormat } from '@betgames/bg-tools';
import { BettingOptionStatus } from '@bg-shared/enums/BettingOptionStatus';
import { IBaccaratDraw } from '@bg-shared/interfaces/Draws/Baccarat';
import { mapWebSocketsBettingOptions } from '../utils/mapWebSocketsBettingOptions';
import { IBaccaratDraftState } from './interfaces';

export const mutations = {
    updateState: (
        draft: IBaccaratDraftState,
        data: IBaccaratDraw,
        oddsFormat: BettingOptionsFormat,
    ): void => {
        if (draft.data.runId !== data.run.id) {
            draft.data.nextDrawShuffleDeck = false;
        }

        draft.data.results = null;
        draft.data.cards = data.run.cards;
        draft.data.drawCode = data.run.code;
        draft.data.runId = data.run.id;
        draft.data.roundId = data.run.roundId;
        draft.data.runRoundId = data.run.runRoundId;

        const bettingOptions = mapWebSocketsBettingOptions({
            options: data.odds,
            gameId: GameId.BACCARAT,
            format: oddsFormat,
        });

        Object.keys(draft.data.bettingOptions).forEach((idString) => {
            const id = Number(idString);
            Object.assign(draft.data.bettingOptions[id], bettingOptions[id]);
        });
    },

    setResults: (draft: IBaccaratDraftState, data: IBaccaratDraw): void => {
        draft.data.results = data.prevRun.results;
        draft.data.cards = data.prevRun.lastCards;

        Object.values(draft.data.bettingOptions).forEach((option) => {
            const idString = option.id.toString();

            if (data.prevRun.pushOdds.includes(idString)) {
                option.status = BettingOptionStatus.PUSH;
            } else if (data.prevRun.wonOdds.includes(idString)) {
                option.status = BettingOptionStatus.WON;
            } else {
                option.status = BettingOptionStatus.LOST;
            }
        });
    },
};
