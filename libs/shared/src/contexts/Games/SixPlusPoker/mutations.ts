import { GameId } from '@bg-shared/enums/GameId';
import { BettingOptionsFormat } from '@betgames/bg-tools';
import { BettingOptionStatus } from '@bg-shared/enums/BettingOptionStatus';
import { CardValue } from '@bg-shared/enums/CardValue';
import { CARD_SHORT_VALUE_10 } from '@bg-shared/constants/constants';
import { ISixPlusPokerDraw } from '@bg-shared/interfaces/Draws/SixPlusPoker';
import { mapWebSocketsBettingOptions } from '../utils/mapWebSocketsBettingOptions';
import { IDraftState } from './interfaces';

export const mutations = {
    updateState: (
        draft: IDraftState,
        data: ISixPlusPokerDraw,
        oddsFormat: BettingOptionsFormat,
    ): void => {
        draft.data.results = null;
        draft.data.wonCardsIds = null;
        draft.data.wonCardsShortened = null;
        draft.data.cards = data.run.cards;
        draft.data.drawCode = data.run.code;
        draft.data.runId = data.run.id;
        draft.data.roundId = data.run.roundId;
        draft.data.runRoundId = data.run.runRoundId;

        const bettingOptions = mapWebSocketsBettingOptions({
            options: data.odds,
            gameId: GameId.SIX_PLUS_POKER,
            format: oddsFormat,
        });

        Object.keys(draft.data.bettingOptions).forEach((idString) => {
            const id = Number(idString);
            Object.assign(draft.data.bettingOptions[id], bettingOptions[id]);
        });
    },

    setResults: (draft: IDraftState, data: ISixPlusPokerDraw): void => {
        draft.data.results = data.prevRun.results;
        draft.data.wonCardsIds = data.prevRun.wonCards;

        // TODO fix: new cards doesn't have id, so creating shortened version
        draft.data.wonCardsShortened = data.prevRun.wonCards?.map((cardId) => {
            const wonCard = data.prevRun.lastCards.find(
                (lastCard) => Number(lastCard.id) === cardId,
            );

            return `${
                wonCard.value === CardValue.TEN ? CARD_SHORT_VALUE_10 : wonCard.value.charAt(0)
            }${wonCard.suit.charAt(0)}`;
        });
        draft.data.cards = data.prevRun.lastCards;

        Object.values(draft.data.bettingOptions).forEach((option) => {
            option.status = data.prevRun.wonOdds.includes(option.id.toString())
                ? BettingOptionStatus.WON
                : BettingOptionStatus.LOST;
        });
    },
};
