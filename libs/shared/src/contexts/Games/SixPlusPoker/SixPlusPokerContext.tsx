import { useMemo, useRef, useEffect } from 'react';
import { createContainer } from '@bg-shared/contexts/createContainer';
import { taxes, useImmutableState } from '@betgames/bg-tools';
import { ISixPlusPokerDraw } from '@bg-shared/interfaces/Draws/SixPlusPoker';
import { webSocketsRepository } from '@bg-shared/infrastructure/WebSockets';
import { GameId } from '@bg-shared/enums/GameId';
import { GAME_INFO_CHANNELS } from '@bg-shared/constants/socketChannels';
import { HOLD_DRAW_UPDATE_TIME } from '@bg-shared/constants/constants';
import { sessionStore } from '@bg-shared/business/Session';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { gamesStatusService } from '@bg-shared/business/GamesStatus';
import { balanceSelectors, balanceStore } from '@bg-shared/business/Balance';
import { isGameActive } from '@bg-shared/utils/isGameActive';
import { webAPIIntegration } from '@bg-shared/integrations';
import { mapPropsToState } from './mapPropsToState';
import { ISixPlusPokerProps, ISixPlusPokerContext, ISixPlusPokerGameState } from './interfaces';
import { mutations } from './mutations';

export const SixPlusPokerContext: ISixPlusPokerContext = createContainer<
    ISixPlusPokerGameState,
    ISixPlusPokerProps
>((props) => {
    const webSockets = webSocketsRepository;
    const gameSettings = partnerSettingsStore.getEnabledGame(GameId.SIX_PLUS_POKER);
    const [state, dispatch] = useImmutableState<ISixPlusPokerGameState>(
        useMemo(() => mapPropsToState(props, sessionStore.oddsFormat), []),
    );

    const stateRunIdRef = useRef<number>(state.data.runId);
    stateRunIdRef.current = state.data.runId;

    useEffect(() => {
        gamesStatusService.configureTimer({
            gameId: GameId.SIX_PLUS_POKER,
            seconds: props.draw.run.secondsLeft,
            timestamp: props.draw.infoTime,
        });

        webSockets.on<ISixPlusPokerDraw>(
            `${GAME_INFO_CHANNELS[GameId.SIX_PLUS_POKER]}:${gameSettings.presetId}:${
                taxes.schemeId
            }`,
            (data) => {
                const isNewRun = !!data.prevRun.results;

                setTimeout(() => {
                    if (isNewRun) {
                        dispatch((draft) => {
                            mutations.setResults(draft, data);
                        });
                    }

                    setTimeout(
                        () => {
                            if (isGameActive(GameId.SIX_PLUS_POKER) && isNewRun) {
                                const params = {
                                    balance: balanceSelectors.getBalance(GameId.SIX_PLUS_POKER)(
                                        balanceStore.value,
                                    ),
                                    prevRunId: stateRunIdRef.current,
                                };

                                webAPIIntegration(
                                    partnerSettingsStore.webIntegrationType,
                                    (api) => {
                                        api.runFinished(params);
                                    },
                                );
                            }

                            gamesStatusService.updateTimer({
                                gameId: GameId.SIX_PLUS_POKER,
                                seconds: data.run.secondsLeft,
                                timestamp: data.infoTime,
                            });

                            dispatch((draft) => {
                                mutations.updateState(draft, data, sessionStore.oddsFormat);
                            });
                        },
                        isNewRun ? HOLD_DRAW_UPDATE_TIME[GameId.SIX_PLUS_POKER] : 0,
                    );
                }, gameSettings.streamDelay);
            },
        );
    }, []);

    return [state, dispatch];
}, 'SixPlusPokerContext');
