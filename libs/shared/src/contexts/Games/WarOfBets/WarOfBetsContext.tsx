import { useMemo, useRef, useEffect } from 'react';
import { createContainer } from '@bg-shared/contexts/createContainer';
import { taxes, useImmutableState } from '@betgames/bg-tools';
import { IWarOfBetsDraw } from '@bg-shared/interfaces/Draws/WarOfBets';
import { webSocketsRepository } from '@bg-shared/infrastructure/WebSockets';
import { GameId } from '@bg-shared/enums/GameId';
import { GAME_INFO_CHANNELS, WAR_OF_BETS_SHUFFLE_DECK } from '@bg-shared/constants/socketChannels';
import { HOLD_DRAW_UPDATE_TIME } from '@bg-shared/constants/constants';
import { sessionStore } from '@bg-shared/business/Session';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { gamesStatusService } from '@bg-shared/business/GamesStatus';
import { balanceSelectors, balanceStore } from '@bg-shared/business/Balance';
import { isGameActive } from '@bg-shared/utils/isGameActive';
import { webAPIIntegration } from '@bg-shared/integrations';
import { IWarOfBetsProps, IWarOfBetsContext, IWarOfBetsGameState } from './interfaces';
import { mapPropsToState } from './mapPropsToState';
import { mutations } from './mutations';

export const WarOfBetsContext: IWarOfBetsContext = createContainer<
    IWarOfBetsGameState,
    IWarOfBetsProps
>((props) => {
    const webSockets = webSocketsRepository;
    const gameSettings = partnerSettingsStore.getEnabledGame(GameId.WAR);
    const [state, dispatch] = useImmutableState<IWarOfBetsGameState>(
        useMemo(() => mapPropsToState(props, sessionStore.oddsFormat), []),
    );

    const stateRunIdRef = useRef<number>(state.data.runId);
    stateRunIdRef.current = state.data.runId;

    useEffect(() => {
        gamesStatusService.configureTimer({
            gameId: GameId.WAR,
            seconds: props.draw.run.secondsLeft,
            timestamp: props.draw.infoTime,
        });

        webSockets.on<IWarOfBetsDraw>(
            `${GAME_INFO_CHANNELS[GameId.WAR]}:${gameSettings.presetId}:${taxes.schemeId}`,
            (data) => {
                const isNewRun = !!data.prevRun.results;

                setTimeout(() => {
                    if (isNewRun) {
                        dispatch((draft) => {
                            mutations.setResults(draft, data);
                        });
                    }

                    setTimeout(
                        () => {
                            if (isGameActive(GameId.WAR) && isNewRun) {
                                const params = {
                                    balance: balanceSelectors.getBalance(GameId.WAR)(
                                        balanceStore.value,
                                    ),
                                    prevRunId: stateRunIdRef.current,
                                };

                                webAPIIntegration(
                                    partnerSettingsStore.webIntegrationType,
                                    (api) => {
                                        api.runFinished(params);
                                    },
                                );
                            }

                            gamesStatusService.updateTimer({
                                gameId: GameId.WAR,
                                seconds: data.run.secondsLeft,
                                timestamp: data.infoTime,
                            });

                            dispatch((draft) => {
                                mutations.updateState(draft, data, sessionStore.oddsFormat);
                            });
                        },
                        isNewRun ? HOLD_DRAW_UPDATE_TIME[GameId.WAR] : 0,
                    );
                }, gameSettings.streamDelay);
            },
        );

        webSockets.on<void>(WAR_OF_BETS_SHUFFLE_DECK, () => {
            dispatch((draft) => {
                draft.data.nextDrawShuffleDeck = true;
            });
        });
    }, []);

    return [state, dispatch];
}, 'WarOfBetsContext');
