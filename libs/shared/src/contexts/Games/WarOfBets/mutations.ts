import { GameId } from '@bg-shared/enums/GameId';
import { BettingOptionsFormat } from '@betgames/bg-tools';
import { BettingOptionStatus } from '@bg-shared/enums/BettingOptionStatus';
import { IWarOfBetsDraw } from '@bg-shared/interfaces/Draws/WarOfBets';
import { mapWebSocketsBettingOptions } from '../utils/mapWebSocketsBettingOptions';
import { IWarOfBetsDraftState } from './interfaces';

export const mutations = {
    updateState: (
        draft: IWarOfBetsDraftState,
        data: IWarOfBetsDraw,
        oddsFormat: BettingOptionsFormat,
    ): void => {
        if (draft.data.runId !== data.run.id) {
            draft.data.nextDrawShuffleDeck = false;
        }

        draft.data.results = null;
        draft.data.cards = data.run.cards;
        draft.data.drawCode = data.run.code;
        draft.data.runId = data.run.id;
        draft.data.roundId = data.run.roundId;
        draft.data.runRoundId = data.run.runRoundId;

        const bettingOptions = mapWebSocketsBettingOptions({
            options: data.odds,
            gameId: GameId.WAR,
            format: oddsFormat,
        });

        Object.keys(draft.data.bettingOptions).forEach((idString) => {
            const id = Number(idString);
            Object.assign(draft.data.bettingOptions[id], bettingOptions[id]);
        });
    },

    setResults: (draft: IWarOfBetsDraftState, data: IWarOfBetsDraw): void => {
        draft.data.results = data.prevRun.results;
        draft.data.cards = data.prevRun.lastCards;

        Object.values(draft.data.bettingOptions).forEach((option) => {
            option.status = data.prevRun.wonOdds.includes(option.id.toString())
                ? BettingOptionStatus.WON
                : BettingOptionStatus.LOST;
        });
    },
};
