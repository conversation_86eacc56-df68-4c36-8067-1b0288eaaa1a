import { useMemo, useEffect } from 'react';
import { keyBy } from 'lodash-unified';
import { createContainer } from '@bg-shared/contexts/createContainer';
import { ClassicGameDefinition } from '@bg-shared/utils/ClassicGameDefinition';
import { webSocketsRepository } from '@bg-shared/infrastructure/WebSockets';
import { useImmutableState } from '@betgames/bg-tools';
import { GAME_INFO_CHANNELS } from '@bg-shared/constants/socketChannels';
import { ILiveLotteryDraw } from '@bg-shared/interfaces/Draws/ILiveLotteryDraw';
import { partnerSettingsStore } from '@bg-shared/business/PartnerSettings';
import { GameId } from '@bg-shared/enums/GameId';
import { gamesStatusService } from '@bg-shared/business/GamesStatus';
import { GAME_STOP_BETTING_OFFSET } from '@bg-shared/constants/constants';
import { gamesRunsEntity } from '@bg-shared/business/GamesRuns';
import { gamesOptionsEntity } from '@bg-shared/business/GamesOptions';
import { balanceStore, balanceSelectors } from '@bg-shared/business/Balance';
import { isGameActive } from '@bg-shared/utils/isGameActive';
import { webAPIIntegration } from '@bg-shared/integrations/WebIntegration';
import { sessionStore } from '@bg-shared/business/Session';
import { mapToGroupsIds } from '@bg-shared/business/GamesOptions/utils/mapToGroupsIds';
import { ILiveLotteryContext, ILiveLotteryGameState, ILiveLotteryProps } from './interfaces';
import { mapPropsToState } from './mapPropsToState';
import { mutations } from './mutations';

export const createLiveLotteryContext = (gameId: GameId): ILiveLotteryContext =>
    createContainer<ILiveLotteryGameState, ILiveLotteryProps>((props) => {
        const webSockets = webSocketsRepository;
        const delay = partnerSettingsStore.getGameDelay(gameId);
        const [state, dispatch] = useImmutableState<ILiveLotteryGameState>(
            useMemo(() => mapPropsToState(props, sessionStore.oddsFormat, gameId), []),
        );

        useEffect(() => {
            gamesRunsEntity.store(gameId).update({
                run: {
                    id: state.data.runId,
                    code: state.data.drawCode,
                },
            });

            gamesOptionsEntity.store(gameId).update({
                options: keyBy(
                    props.bettingOptions.groups.map((group) => group.options).flat(),
                    'id',
                ),
                groups: mapToGroupsIds(props.bettingOptions.groups),
                items: props.bettingOptions.items,
            });

            gamesStatusService.configureTimer({
                gameId,
                seconds: props.draw.secondsLeft,
                timestamp: props.draw.infoTime,
                offsetSeconds: ClassicGameDefinition.isCasinoView(gameId)
                    ? GAME_STOP_BETTING_OFFSET
                    : 0,
            });

            webSockets.on<ILiveLotteryDraw>(GAME_INFO_CHANNELS[gameId], (data) => {
                setTimeout(() => {
                    if (isGameActive(gameId)) {
                        const params = {
                            balance: balanceSelectors.getBalance(gameId)(balanceStore.value),
                            prevRunId: gamesRunsEntity.store(gameId).value.run.id,
                        };

                        webAPIIntegration(partnerSettingsStore.webIntegrationType, (api) => {
                            api.runFinished(params);
                        });
                    }

                    dispatch((draft) => {
                        mutations.updateState(draft, data);
                    });

                    gamesRunsEntity.store(gameId).update({
                        run: {
                            id: data.runId,
                            code: data.drawCode,
                        },
                    });

                    gamesStatusService.updateTimer({
                        gameId,
                        seconds: data.secondsLeft,
                        timestamp: data.infoTime,
                    });
                }, delay);
            });
        }, []);

        return [state, dispatch];
    }, `LiveLotteryContext:${gameId}`);
