import { ILotteryGameState } from '@bg-shared/contexts/Games/Lottery/interfaces';
import { ILiveLotteryGameState, ILiveLotterySelectedState } from './interfaces';

export const liveLotterySelectors = {
    selectData: (state: ILiveLotteryGameState): ILiveLotterySelectedState => ({
        ...state.data,
        gameItems: state.gameItems,
        oddsGroups: state.oddGroups,
    }),

    getBettingOptionGroupId:
        (optionId: number) =>
        (state: ILotteryGameState): number =>
            state.oddGroups.find((group) => group.ids.includes(optionId))?.id,
};
