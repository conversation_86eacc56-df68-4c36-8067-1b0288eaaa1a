import { JSX } from 'react';
import { ErrorBoundary } from '@bg-components/ErrorBoundary/ErrorBoundary'; // TODO - reference to another library
import { useAfterUpdate } from '@betgames/bg-tools';
import { useLocation } from 'react-router-dom';
import { useUpdateOnModalsChange } from '@bg-shared/hooks/useUpdateOnModalsChange';
import { ModalsManager } from './ModalsManager';

interface IProps {
    modalManager: ModalsManager;
}

export const ModalsContainer = (props: IProps): JSX.Element => {
    const location = useLocation();

    useUpdateOnModalsChange(props.modalManager);

    useAfterUpdate(() => {
        props.modalManager.removeAll();
    }, [location.pathname]);

    return (
        <>
            {props.modalManager.modals.map((currentModal) => (
                <ErrorBoundary key={currentModal.name} ErrorComponent={null}>
                    <currentModal.Component
                        /* eslint-disable-next-line react/jsx-props-no-spreading */
                        {...currentModal.props}
                        onClose={(...params) => {
                            props.modalManager.close(currentModal.name, params);
                        }}
                    />
                </ErrorBoundary>
            ))}
        </>
    );
};
