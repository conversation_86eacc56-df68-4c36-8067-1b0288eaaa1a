import { createContext, PropsWithChildren, JSX, useContext } from 'react';
import { ModalsContainer } from './ModalsContainer';
import { ModalsManager } from './ModalsManager';

const ModalsManagerContext = createContext(undefined);

interface IProps {
    manager: ModalsManager;
}

export const ModalsManagerProvider = (props: PropsWithChildren<IProps>): JSX.Element => (
    <ModalsManagerContext.Provider value={props.manager}>
        <ModalsContainer modalManager={props.manager} />
        {props.children}
    </ModalsManagerContext.Provider>
);

export function useModalsManager<T = ModalsManager>(): T {
    const context = useContext(ModalsManagerContext);

    if (context === undefined) {
        throw new Error('useModalsManagerContext must be used within a ModalsManagerProvider');
    }

    return context;
}
