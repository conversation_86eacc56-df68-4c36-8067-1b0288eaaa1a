import { JSX, ComponentProps } from 'react';
import { forceCast, setImmediateTimeout } from '@betgames/bg-tools';
import { PubSub } from '@bg-shared/utils/PubSub';
import { IModalProps, IModalItem } from './interfaces';

type Primitive = number | string;

export class ModalsManager {
    private currentModals = new Map<Primitive, IModalItem>();

    private subscriptions = new PubSub();

    private setModalToClose(name: Primitive, args?: unknown[]) {
        const modal = this.currentModals.get(name);

        if (modal) {
            modal.props?.onClose?.apply(null, args || []);
            modal.hide = true;
        }
    }

    private callModalsChangeEvents(callback?: ICallback): void {
        this.subscriptions.publish();

        if (callback) {
            setImmediateTimeout(callback);
        }
    }

    public subscribeOnModalsChange(callback: () => void): () => void {
        return this.subscriptions.subscribe(callback);
    }

    public get modals(): IModalItem[] {
        return Array.from(this.currentModals.values());
    }

    public getModal(name: Primitive): IModalItem {
        return this.currentModals.get(name);
    }

    public hasModal(name: Primitive): boolean {
        return this.currentModals.has(name);
    }

    public open<Props>(
        name: Primitive,
        Component: (props: IModalProps<Props>) => JSX.Element,
        props?: ComponentProps<typeof Component>,
    ): void {
        if (this.currentModals.has(name)) {
            return;
        }

        this.currentModals.set(name, {
            name,
            Component: forceCast<(props: IModalProps) => JSX.Element>(Component),
            props,
        });

        this.callModalsChangeEvents();
    }

    public toggle<Props>(
        name: string,
        Component: (props: IModalProps<Props>) => JSX.Element,
        props?: ComponentProps<typeof Component>,
    ): void {
        if (this.hasModal(name)) {
            this.close(name);
        } else {
            this.open(name, Component, props);
        }
    }

    public close(name: Primitive | Primitive[], args?: unknown[]): void {
        const names = Array.isArray(name) ? name : [name];
        const hasModalsToClose = names.some((modalName) => this.hasModal(modalName));

        names.forEach((modalName) => {
            this.setModalToClose(modalName, args);
        });

        if (hasModalsToClose) {
            this.callModalsChangeEvents();
        }
    }

    public closeAll(): void {
        this.currentModals.forEach((modal, name) => {
            this.setModalToClose(name);
        });

        this.callModalsChangeEvents();
    }

    public remove(name: Primitive, callback?: ICallback): void {
        this.onRemove(name);
        this.currentModals.delete(name);
        this.callModalsChangeEvents(callback);
    }

    public onRemove(name: Primitive) {
        const modal = this.currentModals.get(name);

        if (modal) {
            modal?.props?.onRemove?.apply(null, []);
        }
    }

    public removeAll(): void {
        this.currentModals.clear();
        this.callModalsChangeEvents();
    }
}
