import { JSX, useState, useRef, useCallback } from 'react';
import classNames from 'classnames';
import { DirectionalIcon, IconDirection } from '@bg-components/Icon';
import { ScrollableContent } from '@bg-components/ScrollableContent';
import { CustomCssClassNames, useElementOnResize, IDropdownOption } from '@bg-shared';
import classes from './Dropdown.module.scss';

interface IProps<T> {
    options: IDropdownOption<T>[];
    onChange: (value: T) => void;
    value: number | string;
    icon: IIconComponent;
    renderOption: (option: IDropdownOption<T>) => JSX.Element | string | number;
    renderButton: JSX.Element;
    dataQa?: string;
    className?: {
        container?: string;
        open?: string;
        button?: string;
        content?: string;
        item?: string;
        icon?: string;
        selected?: string;
        shadow?: string;
    };
}

export const Dropdown = <T,>(props: IProps<T>): JSX.Element => {
    const [open, setOpen] = useState<boolean>(false);
    const containerRef = useRef<HTMLDivElement>(null);
    const toggleButtonRef = useRef<HTMLButtonElement>(null);

    const updateDropdownPosition = useCallback((): void => {
        const buttonRect = toggleButtonRef.current?.getBoundingClientRect();

        if (buttonRect) {
            const { top, height, width } = buttonRect;

            containerRef.current.style.setProperty('--dropdown-width', `${width}px`);
            containerRef.current.style.setProperty('--dropdown-top-offset', `${top + height}px`);
        }
    }, []);

    const toggle = (): void => {
        updateDropdownPosition();
        setOpen(!open);
    };
    console.log('test');

    useElementOnResize(toggleButtonRef, updateDropdownPosition);

    return (
        <div
            className={classNames(classes.dropdown, props.className?.container)}
            ref={containerRef}
        >
            <button
                onClick={toggle}
                type="button"
                ref={toggleButtonRef}
                className={classNames(
                    CustomCssClassNames.DROPDOWN_BUTTON,
                    classes.dropdownButton,
                    props.className?.button,
                    {
                        [props.className?.shadow]: open,
                        [props.className?.open]: open,
                    },
                )}
                data-qa={props.dataQa}
            >
                {props.renderButton}
                <DirectionalIcon
                    icon={props.icon}
                    direction={open ? IconDirection.UP : IconDirection.DOWN}
                    className={classNames(props.className?.icon, classes.icon)}
                />
            </button>
            <ScrollableContent
                className={classNames(
                    classes.content,
                    props.className?.content,
                    { [classes.show]: open, [props.className?.shadow]: open },
                    CustomCssClassNames.DROPDOWN_CONTENT,
                )}
            >
                {props.options.map((option) => (
                    <button
                        key={option.value as string}
                        data-qa={`${props.dataQa}-${option.value}`}
                        className={classNames(classes.item, props.className?.item, {
                            [classes.selected]: option.value === props.value,
                            [props.className?.selected]: option.value === props.value,
                        })}
                        type="button"
                        onClick={() => {
                            props.onChange(option.value);
                            setOpen(false);
                        }}
                    >
                        {props.renderOption(option)}
                    </button>
                ))}
            </ScrollableContent>
        </div>
    );
};
