{
    "compileOnSave": false,
    "compilerOptions": {
        "typeRoots": [
            "./workspace/types",
            "./node_modules/@types",
            "./node_modules/@nx/react/typings"
        ],
        "types": ["global.iframe", "express", "timer", "cssmodule", "image", "node"],
        "rootDir": ".",
        "baseUrl": ".",
        "skipLibCheck": true,
        "skipDefaultLibCheck": true,
        "allowSyntheticDefaultImports": true,
        "esModuleInterop": true,
        "allowJs": true,
        "checkJs": false,
        "declaration": false,
        "forceConsistentCasingInFileNames": true,
        "importHelpers": true,
        "noEmitHelpers": true,
        "jsx": "react-jsx",
        "jsxImportSource": "react",
        "target": "es6",
        "useDefineForClassFields": true,
        "lib": ["dom", "esnext"],
        "experimentalDecorators": true,
        "emitDecoratorMetadata": true,
        "module": "esnext",
        "moduleResolution": "node",
        "noEmitOnError": true,
        "noFallthroughCasesInSwitch": true,
        "noUnusedLocals": true,
        "strict": true,
        "pretty": true,
        "removeComments": true,
        "strictNullChecks": false,
        "sourceMap": true,
        "resolveJsonModule": true,
        "paths": {
            "@bg-components/*": ["libs/components/src/components/*"],
            "@bg-configs/*": ["configs/*"],
            "@bg-services": ["libs/services/src/index.ts"],
            "@bg-services/*": ["libs/services/src/*"],
            "@bg-shared": ["libs/shared/src/index.ts"],
            "@bg-shared/*": ["libs/shared/src/*"],

            "@iframe-branding/*": ["apps/iframe-server/src/middlewares/config/branding/*"],
            "@iframe-shell/*": ["apps/iframe-shell/src/*"],
            "@iframe-remotes/*": ["apps/iframe-remotes/*"],
            "__mocks__/*": ["libs/shared/__mocks__/*"],
            "styles/*": ["libs/shared/src/styles/*"]
        }
    },
    "exclude": ["node_modules", "tmp", "rspack.config.ts"],
}
